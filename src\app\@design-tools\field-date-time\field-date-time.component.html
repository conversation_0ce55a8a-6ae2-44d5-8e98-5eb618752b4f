<div class="form-control-group">
  <label
    class="label"
    *ngIf="label"
    [attr.aria-label]="label"
    for="{{ name }}"
    >{{ label }}</label
  >
  <div class="position-relative input-group">
    <div
      nbInput
      fieldSize="small"
      class="input-full-width size-small status-basic shape-rectangle ng-untouched ng-pristine ng-valid ng-star-inserted nb-transition"
      (click)="datetimeInput.opened = true"
    >
      <dx-date-box
        #datetimeInput
        [class]="class + ' p-0 m-0'"
        [placeholder]="placeholder"
        type="datetime"
        pickerType="list"
        stylingMode="filled"
        [useMaskBehavior]="true"
        [disabled]="disabled"
        [readOnly]="readonly"
        [required]="required"
        [(ngModel)]="currentValue"
        (ngModelChange)="changeValue(currentValue)"
        [dateSerializationFormat]="'yyyy-MM-ddTHH:mm:ss'"
      >
      </dx-date-box>
    </div>
    <nb-icon
      [options]="{ animation: { type: 'pulse' } }"
      icon="calendar"
      status="info"
      class="absolute-right pointer calendar-accessor"
      (click)="datetimeInput.opened = true"
    ></nb-icon>
  </div>
</div>
