<dx-data-grid
  id="usuarioGrid"
  [dataSource]="gridData"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="false"
  [remoteOperations]="true"
  keyExpr="uuid"
>
  <dxo-paging [pageSize]="10"></dxo-paging>
  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <dxo-filter-row [visible]="true"></dxo-filter-row>

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <!-- <dxo-search-panel
    [visible]="true"
    placeholder="Buscar usuário"
  ></dxo-search-panel> -->

  <dxo-editing
    mode="form"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="false"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column dataField="municipioNome" caption="Municipio"></dxi-column>

  <dxi-column dataField="entidadeNome" caption="Entidade"></dxi-column>

  <dxi-column dataField="exercicioNome" caption="Exercicio"></dxi-column>

  <dxi-column dataField="ip" caption="IP"></dxi-column>

  <dxi-column
    dataField="tokenCriadoEm"
    caption="Data de criação"
    dataType="datetime"
    sortOrder="desc"
  ></dxi-column>

  <dxi-column
    dataField="tokenValidoAte"
    caption="Valida ate"
    dataType="datetime"
  ></dxi-column>

  <dxi-column
    dataField="tokenFinalizadoEm"
    caption="Finalizado em"
    dataType="datetime"
  ></dxi-column>

  <dxi-column
    dataField="idToken"
    caption=""
    [width]="80"
    [allowFiltering]="false"
    [allowSorting]="false"
    cellTemplate="acaoColumn"
  ></dxi-column>

  <div *dxTemplate="let data of 'acaoColumn'">
    <a
      *ngIf="nivelPermissao === 'FULL' && finalizado(data)"
      (click)="removerSassao(data)"
      class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
    >
    </a>
  </div>
</dx-data-grid>
