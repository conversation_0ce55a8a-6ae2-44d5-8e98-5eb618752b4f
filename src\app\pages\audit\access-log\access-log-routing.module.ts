import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AccessLogComponent } from './access-log.component';
import { AccessLogSearchComponent } from './access-log-search/access-log-search.component';

const routes: Routes = [
  {
    path: '',
    component: AccessLogSearchComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AccessLogRoutingModule { }
