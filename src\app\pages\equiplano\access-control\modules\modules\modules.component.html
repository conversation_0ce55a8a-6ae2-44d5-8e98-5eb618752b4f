<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
    <div class="mt-2">
      <eqp-breadcrumb></eqp-breadcrumb>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="modulo">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="descricao"
          name="Descrição"
          label="Descrição"
          placeholder="Descrição"
          required="true"
          errorMessage="É obrigatório preencher a descrição"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="link"
          name="Link"
          label="Link"
          placeholder="Link"
          required="true"
          errorMessage="É obrigatório preencher o link"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Ícone"
          label="Ícone"
          placeholder="Ícone"
          formControlName="icone"
          [dataSource]="iconsSystems"
          valueExpr="valor"
          displayExpr="texto"
          required="true"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Sistema"
          label="Sistema"
          placeholder="Sistema"
          formControlName="sistema"
          [dataSource]="sistemas"
          valueExpr="chave"
          displayExpr="valor"
          required="true"
        ></eqp-nebular-select>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Status"
          label="Status"
          placeholder="Status"
          formControlName="status"
          [dataSource]="statuses"
          valueExpr="chave"
          displayExpr="valor"
          required="true"
        ></eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="formulario.get('modulo.uuid').value"
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
