dx-select-box.dx-texteditor.dx-editor-outlined {
  background-color: var(--input-basic-background-color);
  border: 1px solid var(--input-basic-border-color);
  color: var(--input-basic-text-color);
  box-sizing: border-box;
  box-shadow: none !important;
  border-radius: 0.25rem;
  font-family: var(--input-text-font-family);

  &.dx-state-hover {
    background-color: var(--input-basic-hover-background-color);
    border-color: var(--input-basic-hover-border-color);
  }

  &.dx-state-focused {
    background-color: var(--input-basic-focus-background-color);
    border-color: var(--input-basic-focus-border-color);
  }

  .dx-placeholder::before,
  .dx-texteditor-input {
    height: 30px;
    padding: 6px 11px 8px;
    font-family: var(--input-text-font-family);
  }

  .dx-texteditor-input {
    font-size: var(--input-small-text-font-size);
    font-weight: var(--input-small-text-font-weight);
    color: var(--input-basic-text-color);
  }

  .dx-placeholder {
    font-size: var(--input-small-placeholder-text-font-size);
    font-weight: var(--input-small-placeholder-text-font-weight);
    color: var(--input-basic-placeholder-text-color);
  }
}
