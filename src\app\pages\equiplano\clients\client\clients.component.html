<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
    <div class="mt-2">
      <eqp-breadcrumb></eqp-breadcrumb>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="cliente">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="nome"
          name="Nome"
          label="Nome"
          placeholder="Nome"
          required="true"
          errorMessage="É obrigatório preencher o nome"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="email"
          name="E-mail"
          label="E-mail"
          placeholder="E-mail"
          required="true"
          errorMessage="É obrigatório preencher o e-mail"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="telefone"
          name="Telefone"
          label="Telefone"
          placeholder="Telefone"
          required="true"
          errorMessage="É obrigatório preencher o telefone"
          primaryMask="(00) 0000-0000 9"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="hostname"
          name="Host"
          label="Host"
          placeholder="Host"
          required="true"
          errorMessage="É obrigatório preencher o host"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="logoEmailUrl"
          name="URL logo"
          label="URL logo"
          placeholder="URL logo"
          required="true"
          errorMessage="É obrigatório preencher o URL logo"
        >
        </eqp-nebular-input>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <label
          class="label"
          [attr.aria-label]="'Cor base'"
          for="{{ 'Cor base' }}"
          >Cor base*</label
        >
        <dx-color-box
          formControlName="corBase"
          stylingMode="outlined"
        ></dx-color-box>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <label
          class="label"
          [attr.aria-label]="'Cor texto base'"
          for="{{ 'Cor texto base' }}"
          >Cor texto base*</label
        >
        <dx-color-box
          formControlName="corTextoBase"
          stylingMode="outlined"
        ></dx-color-box>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <label class="label" [attr.aria-label]="'Status'" for="{{ 'Status' }}"
          >Status*</label
        >
        <dx-select-box
          [items]="statuses"
          valueExpr="valor"
          displayExpr="texto"
          formControlName="status"
          stylingMode="outlined"
        ></dx-select-box>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <label
          class="label"
          [attr.aria-label]="'Município'"
          for="{{ 'Município' }}"
          >Município*</label
        >
        <dx-select-box
          [dataSource]="municipios"
          valueExpr="uuid"
          displayExpr="nome"
          formControlName="municipioClienteUuid"
          [searchMode]="'startswith'"
          [searchEnabled]="true"
          stylingMode="outlined"
        ></dx-select-box>
      </div>
      <div class="col-md-4 col-sm-12 mb-4">
        <nb-checkbox formControlName="prefeitura">Prefeitura</nb-checkbox>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="formulario.get('cliente.uuid').value"
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
