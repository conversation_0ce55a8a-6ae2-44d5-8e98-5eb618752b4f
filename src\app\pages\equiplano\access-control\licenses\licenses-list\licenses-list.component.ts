import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import {
  ModalConfirmarComponent,
} from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'

import {
  ToastrService,
} from './../../../../../@common/services/toastr/toastr.service'
import { LicensesService } from './../licenses.service'

@Component({
  selector: 'eqp-licenses-list',
  templateUrl: './licenses-list.component.html',
  styleUrls: ['./licenses-list.component.scss'],
})
export class LicensesListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Controle de acesso | Licenças'

  public gridData: any

  private subscription: Subscription
  private clientsSubscription: Subscription
  private modulesSubscription: Subscription
  constructor(
    private service: LicensesService,
    private toastr: ToastrService,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
  }

  public ngOnInit(): void {
    this.gridData = this.service.getDataSourceFiltro('uuid', 'licenca', 10)
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.clientsSubscription) this.clientsSubscription.unsubscribe()
    if (this.modulesSubscription) this.modulesSubscription.unsubscribe()
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items[0].showText = 'ever'
    event.toolbarOptions.items[0].options.text = 'Nova licença'
    event.toolbarOptions.items[0].options.hint = 'Nova licença'

    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/controle-acesso/licencas/novo`])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/controle-acesso/licencas/edit/${uuid}`])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Licença excluída com sucesso.',
              })
              this.gridData = this.service.getDataSourceFiltro(
                'uuid',
                'licenca',
                10,
              )
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
