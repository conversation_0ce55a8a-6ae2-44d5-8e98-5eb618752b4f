import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { ModulesReturnInterface } from '@pages/equiplano/access-control/modules/modules'
import { ClientReturnInterface } from '@pages/equiplano/clients/clients'
import { Observable } from 'rxjs'

import { AccessGroupInterface } from './../access-group/access-group'
import { AccessGroupReturnInterface, PermissionInterface } from './access-group'

@Injectable({
  providedIn: 'root',
})
export class AccessGroupService extends CrudService {
  constructor(private http: HttpClient) {
    super(http)
  }
  public get(): Observable<AccessGroupReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<AccessGroupReturnInterface>(`grupo_acesso`, {
      headers,
    })
  }

  public getIndividual(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`grupo_acesso/${uuid}`, {
      headers,
    })
  }

  public put(dto: AccessGroupInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<AccessGroupInterface>(
      `grupo_acesso/${dto.uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(dto: AccessGroupInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<AccessGroupInterface>(`grupo_acesso`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: AccessGroupInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<AccessGroupInterface[]>(`grupo_acesso/lote`, batch, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`grupo_acesso/${uuid}`, {
      headers,
    })
  }

  public getClientes(): Observable<ClientReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ClientReturnInterface>('grupo_acesso/cliente', {
      headers,
    })
  }

  public getModulos(): Observable<ModulesReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ModulesReturnInterface>(`grupo_acesso/module`, {
      headers,
    })
  }

  public getAcoes(moduleUuid: string): Observable<AccessGroupReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<AccessGroupReturnInterface>(
      `grupo_acesso/${moduleUuid}/acao`,
      {
        headers,
      },
    )
  }

  public getPermissoes(
    grupoAcessoUuid: string,
  ): Observable<ModulesReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ModulesReturnInterface>(
      `grupo_acesso/${grupoAcessoUuid}/permissao`,
      {
        headers,
      },
    )
  }

  public putPermissao(dto: PermissionInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<PermissionInterface>(
      `grupo_acesso/${dto.uuid}/permissao`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public postPermissao(dto: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<any>(`grupo_acesso/permissao`, dto, {
      headers,
      observe: 'response',
    })
  }

  public deletePermissao(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`grupo_acesso/${uuid}/permissao`, {
      headers,
    })
  }

  public getUsuario(uuid: string): Observable<AccessGroupReturnInterface> {
    const headers = new HttpHeaders()

    return this.http.get<AccessGroupReturnInterface>(
      `grupo_acesso/usuario/${uuid}`,
      {
        headers,
      },
    )
  }

  public postUsuario(
    userUuid: string[],
    accessGroupUuid: string,
  ): Observable<any> {
    const headers = new HttpHeaders()
    const dto = {
      usuariosUuid: userUuid,
      grupoAcessoUuid: accessGroupUuid,
    }
    return this.http.post<any>(`grupo_acesso/usuario`, dto, {
      headers,
      observe: 'response',
    })
  }

  public deleteUsuario(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`usuario/usuario-grupo-acesso/${uuid}`, {
      headers,
    })
  }

  public getCmbUsuario(): Observable<AccessGroupReturnInterface> {
    const headers = new HttpHeaders()

    return this.http.get<AccessGroupReturnInterface>(
      `grupo_acesso/cmb-usuarios`,
      {
        headers,
      },
    )
  }

  public getNivelAcesso(value): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`grupo_acesso/nivel_acesso/${value}`, {
      headers,
    })
  }
}
