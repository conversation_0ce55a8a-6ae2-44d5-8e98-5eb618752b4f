import { Component, OnDestroy, OnInit } from '@angular/core'
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router'
import { NbMenuItem } from '@nebular/theme'
import { Subject} from 'rxjs'

import { MenuService } from '@pages/menu.service'
import { filter, map, take, takeUntil, tap } from 'rxjs/operators'

@Component({
  templateUrl: './search-results.component.html',
  styleUrls: ['./search-results.component.scss'],
})
export class SearchResultsComponent implements OnInit, OnDestroy {

  public searchTerm = this._activatedRoute.snapshot.params['term'];
  public menuList: NbMenuItem[] = [];
  public result: NbMenuItem[] = [];

  private _destroy$: Subject<void> = new Subject<void>()

  constructor(
    private _menuService: MenuService,
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
  ) {}

  ngOnInit(): void {
    this._eventsNavigationEnd();
    this._getMenuList();
  }

  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }

  private _getMenuList() {
    this._menuService.get().pipe(
      take(1),
      map(res => res.dados),
    ).subscribe(res => {
      this.menuList = res;
      this._setResult();
    });
  }

  private _setResult() {
    const term = this._normalizeString(this.searchTerm);

    const getAllVisibleDescendants = (items: NbMenuItem[]): NbMenuItem[] => {
      return items.reduce((acc: NbMenuItem[], item: NbMenuItem) => {
        if (item.link && !item.hidden) {
          acc.push(item);
        }

        if (item.children?.length) {
          acc = acc.concat(getAllVisibleDescendants(item.children));
        }

        return acc;
      }, []);
    };

    const searchMenus = (items: NbMenuItem[], parentTitle: string = ''): NbMenuItem[] =>
      items.reduce((acc: NbMenuItem[], item: NbMenuItem) => {
        const fullTitle = this._normalizeString(`${parentTitle} ${item.title}`);
        const matchesTitle = fullTitle.includes(term);

        if (item.children?.length) {
          const childResults = searchMenus(item.children, fullTitle);

          if (matchesTitle) {
            const allDescendants = getAllVisibleDescendants(item.children);
            return acc.concat(allDescendants);
          }

          return acc.concat(childResults);
        }

        if (item.link && !item.hidden && matchesTitle) {
          return acc.concat(item);
        }

        return acc;
      }, []);

    this.result = searchMenus(this.menuList ?? []);
  }


  private _normalizeString(value: string): string {
    return value
      ?.toUpperCase()
      .normalize('NFD')                 // Separa acentos das letras
      .replace(/[\u0300-\u036f]/g, '')  // Remove os acentos
      .replace(/Ç/g, 'C')               // Trata especificamente o Ç
      .replace(/\s+/g, ' ')             // Remove excesso de espaços
      .trim();
  }

  protected trackByIndex(index: number): number {
    return index;
  }

  private _eventsNavigationEnd() {
    this._router.events
      .pipe(
        takeUntil(this._destroy$),
        filter(e => e instanceof NavigationEnd),
        tap(() => {
          const term = this._activatedRoute.snapshot.params['term'];
          this.searchTerm = term;
          this._setResult();
        })
      )
      .subscribe();
  }

  public goToSelectedItem(link: string): void {
    this._router.navigate([`${link}`]);
  }
}
