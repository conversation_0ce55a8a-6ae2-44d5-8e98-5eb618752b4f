import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import {
  ActionsListComponent,
} from '@pages/equiplano/access-control/actions-list/actions-list.component'
import {
  ModulesListComponent,
} from '@pages/equiplano/access-control/modules/modules-list/modules-list.component'

import {
  AccessGroupComponent,
} from '../../administration/access-group/access-group/access-group.component'
import {
  AccessGroupListComponent,
} from './../../administration/access-group/access-group-list/access-group-list.component'
import { AccessControlComponent } from './access-control.component'
import {
  LicensesListComponent,
} from './licenses/licenses-list/licenses-list.component'
import { LicensesComponent } from './licenses/licenses/licenses.component'
import {
  MicroServicesListComponent,
} from './micro-services-list/micro-services-list.component'
import { ModulesComponent } from './modules/modules/modules.component'
import {
  ParametersListComponent,
} from './parameters-list/parameters-list.component'

const routes: Routes = [
  {
    path: '',
    component: AccessControlComponent,
    children: [
      {
        path: 'modulos',
        component: ModulesListComponent,
      },
      {
        path: 'modulos/novo',
        component: ModulesComponent,
      },
      {
        path: 'modulos/edit/:uuid',
        component: ModulesComponent,
      },
      {
        path: 'acoes',
        component: ActionsListComponent,
      },
      {
        path: 'grupo-acesso',
        component: AccessGroupListComponent,
      },
      {
        path: 'grupo-acesso/novo',
        component: AccessGroupComponent,
      },
      {
        path: 'grupo-acesso/edit/:uuid',
        component: AccessGroupComponent,
      },
      {
        path: 'licencas',
        component: LicensesListComponent,
      },
      {
        path: 'licencas/novo',
        component: LicensesComponent,
      },
      {
        path: 'licencas/edit/:uuid',
        component: LicensesComponent,
      },
      {
        path: 'micro-servicos',
        component: MicroServicesListComponent,
      },
      {
        path: 'parametros',
        component: ParametersListComponent,
      },
      {
        path: 'icones',
        loadChildren: () =>
          import('./icones/icone.module').then(m => m.IconeModule),
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccessControlRoutingModule {}
