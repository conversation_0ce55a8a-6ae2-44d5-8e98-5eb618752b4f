import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { AccessLogSearchService } from '../services/access-log-search.service'
import { finalize, take } from 'rxjs/operators'
import { exportDataGrid } from 'devextreme/excel_exporter'
import { Workbook } from 'exceljs'
import { saveAs } from 'file-saver'
import moment from 'moment-es6'

@Component({
  selector: 'eqp-access-log-search',
  templateUrl: './access-log-search.component.html',
  styleUrls: ['./access-log-search.component.scss'],
})
export class AccessLogSearchComponent implements OnInit {
  public pageTitle = 'Log de Acesso'
  public gridData: any[] = []
  public loading = false
  public model: FormGroup
  private detalhesDados = []

  constructor(
    private builder: FormBuilder,
    private accessLogService: AccessLogSearchService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
  }

  private getNewModel() {
    return this.builder.group(
      {
        periodoInicial: [undefined, [Validators.required]],
        periodoFinal: [undefined, [Validators.required]],
        usuarioUuid: [],
      },
      {
        validator: (fg: FormGroup) => {
          const [d0, d1] = [
            fg.get('periodoInicial').value,
            fg.get('periodoFinal').value,
          ]
          if (new Date(d0).getTime() > new Date(d1).getTime()) {
            fg.get('periodoFinal').setErrors({
              customError:
                'O período inicial não deve ser maior que o período final.',
            })
          } else {
            fg.get('periodoFinal').setErrors(null)
          }
        },
      },
    )
  }

  onExporting(e: any) {
    var workbook = new Workbook()
    var worksheet = workbook.addWorksheet('Log_Acesso')

    let masterRows = []

    exportDataGrid({
      component: e.component,
      worksheet: worksheet,
      topLeftCell: { row: 2, column: 2 },
      keepColumnWidths: true,
      customizeCell: function ({ gridCell, excelCell }) {
        if (
          gridCell.column.dataField === 'nome' &&
          gridCell.rowType === 'data'
        ) {
          excelCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'BEDFE6' },
          }
          masterRows.push({
            rowIndex: excelCell.fullAddress.row + 1,
            data: gridCell.data,
          })
        }

        if (
          gridCell.column.dataField === 'cpf' &&
          gridCell.rowType === 'data'
        ) {
          excelCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'BEDFE6' },
          }
        }
      },
    })
      .then(cellRange => {
        const borderStyle = { style: 'thin', color: { argb: 'FF7E7E7E' } }
        let offset = 0

        const insertRow = (index, offset, outlineLevel) => {
          const currentIndex = index + offset
          const row = worksheet.insertRow(currentIndex, [], 'n')

          for (var j = worksheet.rowCount + 1; j > currentIndex; j--) {
            worksheet.getRow(j).outlineLevel = worksheet.getRow(
              j - 1,
            ).outlineLevel
          }

          row.outlineLevel = outlineLevel

          worksheet.properties.outlineProperties = {
            summaryBelow: false,
            summaryRight: false, 
          };
        
          if (outlineLevel > 0) {
            row.hidden = true;
          }

          return row
        }

        for (var i = 0; i < masterRows.length; i++) {
          let row = insertRow(masterRows[i].rowIndex + i, offset++, 2)
          let columnIndex = cellRange.from.column

          let usuario = this.gridData.find(
            item => item.uuid === masterRows[i].data.uuid,
          )

          worksheet.mergeCells(row.number, columnIndex, row.number, 7)

          const columns = [
            { caption: 'Data', dataField: 'data' },
            { caption: 'Hora', dataField: 'hora' },
            { caption: 'IP', dataField: 'ip' },
            { caption: 'Ação', dataField: 'acao' },
            { caption: 'Observação', dataField: 'objeto' },
          ]

          row = insertRow(masterRows[i].rowIndex + i, offset++, 2)
          columns.forEach((column, currentColumnIndex) => {
            Object.assign(row.getCell(columnIndex + currentColumnIndex), {
              value: column.caption,
              font: { bold: true },
              border: {
                bottom: borderStyle,
                left: borderStyle,
                right: borderStyle,
                top: borderStyle,
              },
            })
          })

          usuario.informacoes.forEach((task, index) => {
            row = insertRow(masterRows[i].rowIndex + i, offset++, 2)
            columns.forEach((column, currentColumnIndex) => {
              let value: any
              if(column.dataField === 'data')
                value = moment(task[column.dataField]).format('DD/MM/YYYY')
              else
                value = task[column.dataField]
              Object.assign(row.getCell(columnIndex + currentColumnIndex), {
                value: value,
                border: {
                  bottom: borderStyle,
                  left: borderStyle,
                  right: borderStyle,
                  top: borderStyle,
                },
              })
            })
          })
          offset--
        }
      })
      .then(function () {
        workbook.xlsx.writeBuffer().then(function (buffer) {
          saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'Log_Acesso.xlsx',
          )
        })
      })
    e.cancel = true
  }

  eraseSearch() {
    this.model.reset()
    this.gridData = []
  }

  public getGridData() {
    const dto = this.model.getRawValue()

    this.loading = true
    this.accessLogService
      .getDataByFilters(dto)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.gridData = res.dados
      })
  }
}
