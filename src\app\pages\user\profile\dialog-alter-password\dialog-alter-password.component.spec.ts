import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogAlterPasswordComponent } from './dialog-alter-password.component';

describe('DialogAlterPasswordComponent', () => {
  let component: DialogAlterPasswordComponent;
  let fixture: ComponentFixture<DialogAlterPasswordComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DialogAlterPasswordComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogAlterPasswordComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
