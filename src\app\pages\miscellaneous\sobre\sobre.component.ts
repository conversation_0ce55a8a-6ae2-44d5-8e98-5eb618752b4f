import { Component } from '@angular/core'
import { first } from 'rxjs/operators'
import { SobreService } from './sobre.service'

@Component({
  selector: 'eqp-sobre',
  templateUrl: './sobre.component.html',
  styleUrls: ['./sobre.component.scss'],
})
export class SobreComponent {
  public versao: string
  public data: string
  constructor(private service: SobreService) {}

  public ngOnInit(): void {
    this.service
      .get()
      .pipe(first())
      .subscribe(data => {
        this.versao = data.dados.versao
        this.data = data.dados.data
      })
  }
}
