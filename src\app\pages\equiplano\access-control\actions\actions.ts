export interface ActionsInterface {
  uuid?: number | string
  ordem?: string | number
  nome?: string
  icone?: string
  pacote?: string
  rota?: string
  somenteEquiplano?: string | 'YES' | 'NO'
  texto?: string
  status?: string | 'ACTIVE' | 'INACTIVE'
  criadoEm?: string
  atualizadoEm?: string
  moduloUuid?: string
  acaoPaiUuid?: string
}

export interface ActionsReturnInterface {
  dados: ActionsInterface[]
  successo: boolean
}
