<div class="input-wrapper">
  <nb-form-field>
    <nb-icon nbPrefix icon="search" pack="eva"></nb-icon>
    <input
      #searchInput
      nbInput
      type="text"
      [style.width.px]="inputWidth"
      [(ngModel)]="inputValue"
      (input)="onInputChange()"
      placeholder="Acesso rápido ao menu"
      nbTooltip="Digite o nome do item para buscar"
      nbTooltipPlacement="bottom"
      nbTooltipTrigger="focus"
    />
  </nb-form-field>
  <span #textMirror class="input-mirror">{{ inputValue || '\u200B' }}</span>
</div>
