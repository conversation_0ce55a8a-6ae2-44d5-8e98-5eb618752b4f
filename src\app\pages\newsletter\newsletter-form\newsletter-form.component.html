<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
        <eqp-breadcrumb></eqp-breadcrumb>
      </div>
    </div>
  </nb-card-header>

  <nb-card-body [formGroup]="model">
    <div class="row">
      <div class="col-md-6 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="titulo"
          name="titulo"
          label="Título"
          placeholder="Título"
          required="true"
          [maxlength]="100"
        ></eqp-nebular-input>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Módulo"
          label="Módulo *"
          placeholder="Módulo"
          formControlName="moduloUuid"
          [dataSource]="modules"
          valueExpr="uuid"
          displayExpr="descricao"
        ></eqp-nebular-select>
      </div>

      <div class="col-md-6 col-sm-12 mb-4">
        <eqp-field-datetime
          [style]="'basic'"
          formControlName="data"
          label="Data *"
          placeholder="00/00/0000 00:00"
          [required]="true"
        ></eqp-field-datetime>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12 col-sm-12">
        <label class="label" for="description">Descrição *</label>
        <dx-html-editor
          id="description"
          formControlName="descricao"
          [tableResizing]="{
            minColumnWidth: 25,
            minRowHeight: 50,
            enabled: true
          }"
        >
          <dxo-toolbar [multiline]="true">
            <dxi-item name="undo"></dxi-item>
            <dxi-item name="redo"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item
              name="size"
              [acceptedValues]="[
                '8pt',
                '10pt',
                '12pt',
                '14pt',
                '18pt',
                '24pt',
                '36pt'
              ]"
            ></dxi-item>
            <dxi-item
              name="font"
              [acceptedValues]="[
                'Arial',
                'Courier New',
                'Georgia',
                'Impact',
                'Lucida Console',
                'Tahoma',
                'Times New Roman',
                'Verdana'
              ]"
            ></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="bold"></dxi-item>
            <dxi-item name="italic"></dxi-item>
            <dxi-item name="strike"></dxi-item>
            <dxi-item name="underline"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="alignLeft"></dxi-item>
            <dxi-item name="alignCenter"></dxi-item>
            <dxi-item name="alignRight"></dxi-item>
            <dxi-item name="alignJustify"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="orderedList"></dxi-item>
            <dxi-item name="bulletList"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item
              name="header"
              [acceptedValues]="[false, 1, 2, 3, 4, 5]"
            ></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="color"></dxi-item>
            <dxi-item name="background"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="link"></dxi-item>
            <dxi-item name="image"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="clear"></dxi-item>
            <dxi-item name="codeBlock"></dxi-item>
            <dxi-item name="blockquote"></dxi-item>
            <dxi-item name="separator"></dxi-item>
            <dxi-item name="insertTable"></dxi-item>
            <dxi-item name="deleteTable"></dxi-item>
            <dxi-item name="insertRowAbove"></dxi-item>
            <dxi-item name="insertRowBelow"></dxi-item>
            <dxi-item name="deleteRow"></dxi-item>
            <dxi-item name="insertColumnLeft"></dxi-item>
            <dxi-item name="insertColumnRight"></dxi-item>
            <dxi-item name="deleteColumn"></dxi-item>
          </dxo-toolbar>
        </dx-html-editor>

        <div *ngIf="exibirErroDescricao" class="invalid-feedback d-block">
          <div>Descrição é obrigatório.</div>
        </div>
      </div>
    </div>
  </nb-card-body>

  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="voltar()">
          Voltar
        </button>
        <button
        type="button"
        class="btn btn-success float-md-right"
        (click)="confirmar()"
        >
        Confirmar
      </button>
      <button
        *ngIf="estaEditando()"
        type="button"
        class="btn btn-danger mr-3 float-md-right"
        (click)="excluir()"
      >
        Excluir
      </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
