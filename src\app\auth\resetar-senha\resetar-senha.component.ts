import { Component, OnInit } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { first } from 'rxjs/operators'
import { LoginService } from '../services/login.service'

@Component({
  selector: 'eqp-resetar-senha',
  templateUrl: './resetar-senha.component.html',
  styleUrls: ['./resetar-senha.component.scss'],
})
export class ResetarSenhaComponent implements OnInit {
  constructor(
    private service: LoginService,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private router: Router,
  ) {}

  public ngOnInit(): void {
    console.log('teste')
    this.resetarSenha()
  }

  public resetarSenha(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      console.log(uuid)

      // this.service
      //   .resetarSenha(uuid)
      //   .pipe(first())
      //   .subscribe(dados => {
      //     //this.router.navigate(['login'])
      //   })
    })
  }
}
