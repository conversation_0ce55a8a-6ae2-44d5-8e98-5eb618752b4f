<!doctype html>
<html>

  <head>
    <meta charset="utf-8">
    <title>...</title>

    <base href="/">

    <meta
      name="viewport"
      content="width=device-width, initial-scale=1"
    >
    <link
      rel="icon"
      type="image/png"
      href="favicon.png"
    >
    <link
      rel="icon"
      type="image/x-icon"
      href="favicon.ico"
    >

    <link
      rel="dx-theme"
      data-theme="material.nebular"
      href="assets/devextreme-css/dx.material.nebular.css"
      data-active="false"
    >
    <link
      rel="dx-theme"
      data-theme="material.nebular.light"
      href="assets/devextreme-css/dx.material.nebular-light.css"
      data-active="true"
    >
    <link
      rel="dx-theme"
      data-theme="material.nebular.dark"
      href="assets/devextreme-css/dx.material.nebular-dark.css"
      data-active="false"
    >
    <link
      rel="dx-theme"
      data-theme="material.nebular.purple"
      href="assets/devextreme-css/dx.material.nebular-purple.css"
      data-active="false"
    >
    <link
      rel="dx-theme"
      data-theme="material.material.light"
      href="assets/devextreme-css/dx.material.material-light.css"
      data-active="false"
    >
    <link
      rel="dx-theme"
      data-theme="material.material.dark"
      href="assets/devextreme-css/dx.material.material-dark.css"
      data-active="false"
    >
    <link
      rel="dx-theme"
      data-theme="material.blue.light.compact"
      href="assets/devextreme-css/dx.material.blue.light.compact.css"
      data-active="true"
    >
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css"
      integrity="sha512-0SPWAwpC/17yYyZ/4HSllgaK7/gg9OlVozq8K7rf3J8LvCjYEEIfzzpnA2/SSjpGIunCSD18r3UhvDcu/xncWA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <link
      rel="manifest"
      href="manifest.webmanifest"
    >
  </head>

  <body>
  <eqp-app>Loading...</eqp-app>

  <style>
    @-webkit-keyframes spin {
      0% {
        transform: rotate(0)
      }

      100% {
        transform: rotate(360deg)
      }
    }

    @-moz-keyframes spin {
      0% {
        -moz-transform: rotate(0)
      }

      100% {
        -moz-transform: rotate(360deg)
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0)
      }

      100% {
        transform: rotate(360deg)
      }
    }

    .spinner {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1003;
      background: #000000;
      overflow: hidden
    }

    .spinner div:first-child {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 150px;
      height: 150px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      box-shadow: 0 3px 3px 0 rgba(173, 67, 39);
      transform: translate3d(0, 0, 0);
      animation: spin 2s linear infinite
    }

    .spinner div:first-child:after,
    .spinner div:first-child:before {
      content: '';
      position: absolute;
      border-radius: 50%
    }

    .spinner div:first-child:before {
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      box-shadow: 0 3px 3px 0 rgb(6, 59, 89);
      -webkit-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite
    }

    .spinner div:first-child:after {
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      box-shadow: 0 3px 3px 0 rgb(56, 80, 56);
      animation: spin 1.5s linear infinite
    }

  </style>
  <div
    id="nb-global-spinner"
    class="spinner"
  >
    <div class="blob blob-0"></div>
    <div class="blob blob-1"></div>
    <div class="blob blob-2"></div>
    <div class="blob blob-3"></div>
    <div class="blob blob-4"></div>
    <div class="blob blob-5"></div>
  </div>

  <noscript>Please enable JavaScript to continue using this
    application.</noscript>

  <div
    vw
    class="enabled"
  >
    <div
      vw-access-button
      class="active"
    ></div>
    <div vw-plugin-wrapper>
      <div class="vw-plugin-top-wrapper"></div>
    </div>
  </div>
  <script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
  <script>
    new window.VLibras.Widget('https://vlibras.gov.br/app');

  </script>
</body>

</html>
