import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NebularSelect } from '@design-tools/interfaces/nebular-select'
import {
  ActionsInterface,
  ActionsReturnInterface,
} from '@pages/equiplano/access-control/actions/actions'
import { ActionsService } from '@pages/equiplano/access-control/actions/actions.service'
import { DxTreeListComponent } from 'devextreme-angular'
import DataSource from 'devextreme/data/data_source'
import { Subscription } from 'rxjs'
import { distinctUntilChanged, finalize } from 'rxjs/operators'

@Component({
  selector: 'eqp-actions',
  templateUrl: './actions.component.html',
  styleUrls: ['./actions.component.scss'],
})
export class ActionsComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {
  public loading: boolean = false
  public gridData: ActionsInterface[] = []
  public columnsTemplate: DxColumnInterface[] = []
  public modules: any
  public statuses: NebularSelect[] = [
    {
      texto: 'Ativo',
      valor: 'ACTIVE',
    },
    {
      texto: 'Inativo',
      valor: 'INACTIVE',
    },
  ]

  public onlyEquiplano: NebularSelect[] = [
    {
      texto: 'Sim',
      valor: 'YES',
    },
    {
      texto: 'Não',
      valor: 'NO',
    },
  ]

  public onlyPrefeitura: NebularSelect[] = [
    {
      texto: 'Sim',
      valor: 'S',
    },
    {
      texto: 'Não',
      valor: 'N',
    },
  ]

  public initialScreen: NebularSelect[] = [
    {
      texto: 'Sim',
      valor: 'S',
    },
    {
      texto: 'Não',
      valor: 'N',
    },
  ]

  public iconInitialScreen: NebularSelect[] = [
    {
      texto: 'Alteração de bens',
      valor: 'alteracao-bens',
    },
    {
      texto: 'Alteração orçamentária',
      valor: 'alteracao-orcamentaria.svg',
    },
    {
      texto: 'Ass. por relatório',
      valor: 'ass-por-relatorio.svg',
    },
    {
      texto: 'Cadastro de bens',
      valor: 'cadastro-bens.svg',
    },
    {
      texto: 'Cadastro de menbros',
      valor: 'cadastro-menbros.svg',
    },
    {
      texto: 'Caixa',
      valor: 'caixa.svg',
    },
    {
      texto: 'Calculo de depreciação',
      valor: 'calculo-depreciacao.svg',
    },
    {
      texto: 'Compensação conciliação bancária',
      valor: 'compensacao-conciliacao.svg',
    },
    {
      texto: 'Consignações',
      valor: 'consignacoes.svg',
    },
    {
      texto: 'Conta bancária',
      valor: 'conta-bancaria.svg',
    },
    {
      texto: 'Convênio estadual',
      valor: 'convenio-estadual.svg',
    },
    {
      texto: 'Convênio federal',
      valor: 'convenio-federal.svg',
    },
    {
      texto: 'Entidade',
      valor: 'entidade.svg',
    },
    {
      texto: 'Fonte de recurso',
      valor: 'fonte-recurso.svg',
    },
    {
      texto: 'Fornecedor',
      valor: 'fornecedor.svg',
    },
    {
      texto: 'Geração arquivos EFD REINF',
      valor: 'geracao-arq-efd-reinf.svg',
    },
    {
      texto: 'Geração arquivos TCE',
      valor: 'geracao-arq-tce.svg',
    },
    {
      texto: 'Integração RH',
      valor: 'integracao-rh.svg',
    },
    {
      texto: 'Integração tributário',
      valor: 'integracao-tributario.svg',
    },
    {
      texto: 'Lançamento contábil',
      valor: 'lancamento-contabil.svg',
    },
    {
      texto: 'Leis de crédito adicional',
      valor: 'lei-credito.svg',
    },
    {
      texto: 'Leis e atos',
      valor: 'leis-atos.svg',
    },
    {
      texto: 'Liquidação',
      valor: 'liquidacao.svg',
    },
    {
      texto: 'LOA despesa',
      valor: 'loa-despesa.svg',
    },
    {
      texto: 'LOA receita',
      valor: 'loa-receita.svg',
    },
    {
      texto: 'Matriz de saldos contábeis',
      valor: 'matriz-saldo.svg',
    },
    {
      texto: 'Movimentação de bens',
      valor: 'movimentacao-bens.svg',
    },
    {
      texto: 'Obras intervenções',
      valor: 'obras-intervencao.svg',
    },
    {
      texto: 'Orgão oficial',
      valor: 'orgao-oficial.svg',
    },
    {
      texto: 'Pagamento',
      valor: 'pagamento.svg',
    },
    {
      texto: 'Pessoa',
      valor: 'pessoa.svg',
    },
    {
      texto: 'Plano contábil',
      valor: 'plano-contabil.svg',
    },
    {
      texto: 'Plano de despesa',
      valor: 'plano-despesa.svg',
    },
    {
      texto: 'Plano de receita',
      valor: 'plano-receita.svg',
    },
    {
      texto: 'Receita orçamentária',
      valor: 'receita-orcamentaria.svg',
    },
    {
      texto: 'Servidor',
      valor: 'servidor.svg',
    },
    {
      texto: 'Transferência entre contas bancárias',
      valor: 'transf-contas-bancarias.svg',
    },
  ]

  public actionsDad: NebularSelect[] = []

  private subscription: Subscription
  private modulesSubscription: Subscription

  @ViewChild('dataTree', { static: false }) dataTree: DxTreeListComponent

  public bloqueia: boolean = false
  public formulario: FormGroup

  constructor(
    private service: ActionsService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
  ) {}

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.columnsTemplate = this.getColumnsTemplate()
    this.getModules()
    this.changeModule()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.modulesSubscription) this.modulesSubscription.unsubscribe()
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      modulo: [''],
    })
  }

  public fetchGrid(uuid): void {
    this.loading = true
    this.subscription = this.service
      .getPorModule(uuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: ActionsReturnInterface) => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getModules(): void {
    this.modules = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'acao/modules', 10),
      paginate: true,
      pageSize: 10,
    })
  }

  private getActionsDad(uuid): void {
    this.loading = true
    this.subscription = this.service
      .getPaiPorModule(uuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: ActionsReturnInterface) => {
          data.dados.forEach(module => {
            this.actionsDad.push({
              texto: module.nome,
              valor: module.uuid,
            })
          })
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'uuid',
        visible: false,
        allowEditing: false,
        allowHiding: false,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Ordem',
        dataField: 'ordem',
        dataType: 'string',
        allowEditing: true,
        allowSorting: false,
        formItem: {
          isRequired: true,
          editorOptions: {
            maxLength: '100',
            mask: '09999999',
            maskChar: ' ',
            showMaskMode: 'onFocus',
            inputAttr: { id: 'Ordem' },
          },
        },
      },
      {
        caption: 'Nome',
        dataField: 'nome',
        allowSorting: false,
        formItem: {
          isRequired: true,
          editorOptions: {
            maxLength: '100',
            inputAttr: { id: 'Nome' },
          },
        },
      },
      {
        caption: 'Pacote ícone',
        dataField: 'pacote',
        allowSorting: false,
        formItem: {
          isRequired: true,
          editorOptions: {
            maxLength: '100',
            inputAttr: { id: 'Pacote ícone' },
          },
        },
      },
      {
        caption: 'Ícone',
        dataField: 'icone',
        allowSorting: false,
        formItem: {
          isRequired: true,
          editorOptions: { maxLength: '100', inputAttr: { id: 'Ícone' } },
        },
      },
      {
        caption: 'Rota',
        dataField: 'rota',
        allowSorting: false,
        visible: false,
        formItem: {
          editorOptions: { maxLength: '100', inputAttr: { id: 'rota' } },
        },
      },
      {
        caption: 'Texto',
        dataField: 'texto',
        allowSorting: false,
        visible: false,
        formItem: {
          isRequired: true,
          editorOptions: { maxLength: '100', inputAttr: { id: 'Texto' } },
        },
      },
      {
        caption: 'Status',
        dataField: 'status',
        allowSorting: false,
        visible: false,
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Status' } },
        },
        lookup: {
          dataSource: this.statuses,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Somente Equiplano',
        dataField: 'somenteEquiplano',
        allowSorting: false,
        visible: false,
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Somente Equiplano' } },
        },
        lookup: {
          dataSource: this.onlyEquiplano,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Ação Pai',
        dataField: 'acaoPaiUuid',
        dataType: 'string',
        allowSorting: false,
        visible: false,
        formItem: {
          editorOptions: { inputAttr: { id: 'Módulo Pai' } },
        },
        lookup: {
          dataSource: this.actionsDad,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Somente Prefeitura',
        dataField: 'somentePrefeitura',
        allowSorting: false,
        visible: false,
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Somente Prefeitura' } },
        },
        lookup: {
          dataSource: this.onlyPrefeitura,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Tela Inicial',
        dataField: 'telaInicial',
        allowSorting: false,
        visible: false,
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Tela inicial' } },
        },
        lookup: {
          dataSource: this.initialScreen,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Ícone da tela Inicial',
        dataField: 'iconeTelaInicial',
        allowSorting: false,
        visible: false,
        formItem: {
          isRequired: false,
          editorOptions: { inputAttr: { id: 'Ícone da tela Inicial' } },
        },
        lookup: {
          dataSource: this.iconInitialScreen,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Ações',
        type: 'buttons',
        buttons: [
          {
            name: 'edit',
            icon: 'fas fa-edit',
          },
          {
            name: 'delete',
            icon: 'fas fa-trash-alt',
          },
        ],
      },
    ]
    return template
  }

  public onToolbarPreparing(event: any): void {
    if (this.formulario.get('modulo').value) {
      event.toolbarOptions.items.forEach((item: any, index) => {
        if (item.name === 'addRowButton') {
          item.showText = 'ever'
          item.options.text = 'Nova ação'
          item.options.hint = 'Nova ação'
        }
        if (item.options) {
          item.options.elementAttr = {
            id: 'action' + index,
          }
        } else {
          item['options'] = {
            elementAttr: {
              id: 'action' + index,
            },
          }
        }
      })
    } else {
      this.dataTree.instance.cancelEditData()
    }
  }

  public onRowinserting(event: any): void {
    this.loading = true
    const {
      ordem,
      nome,
      icone,
      pacote,
      rota,
      somenteEquiplano,
      texto,
      status,
      acaoPaiUuid,
      somentePrefeitura,
      telaInicial,
      iconeTelaInicial,
    } = event.data
    event.cancel = true

    const dto = {
      ordem,
      nome,
      icone,
      pacote,
      rota,
      somenteEquiplano,
      texto,
      status,
      moduloUuid: this.formulario.get('modulo').value,
      acaoPaiUuid,
      somentePrefeitura,
      telaInicial,
      iconeTelaInicial,
    }

    this.subscription = this.service
      .post(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Ação inserida com sucesso.',
          })
          this.fetchGrid(this.formulario.get('modulo').value)
          this.dataTree.instance.cancelEditData()
        },
        (err: any) => {
          if (err.error != null && err.error.causa != null) {
            this.toastr.send({
              error: true,
              message: err.error.causa.descricao,
            })
          } else {
            this.toastr.bulkSend(err.mensagens)
          }
        },
      )
  }

  public onRowUpdating(event: any): void {
    this.loading = true
    const {
      ordem,
      nome,
      icone,
      pacote,
      rota,
      somenteEquiplano,
      texto,
      status,
      acaoPaiUuid,
      somentePrefeitura,
      telaInicial,
      iconeTelaInicial,
    } = event.newData
    event.cancel = true

    const dto = {
      uuid: event.oldData.uuid,
      ordem: ordem != null ? ordem : event.oldData.ordem,
      nome: nome != null ? nome : event.oldData.nome,
      icone: icone != null ? icone : event.oldData.icone,
      pacote: pacote != null ? pacote : event.oldData.pacote,
      rota: rota != null ? rota : event.oldData.rota,
      somenteEquiplano:
        somenteEquiplano != null
          ? somenteEquiplano
          : event.oldData.somenteEquiplano,
      texto: texto != null ? texto : event.oldData.texto,
      moduloUuid: event.oldData.moduloUuid,
      acaoPaiUuid:
        acaoPaiUuid != null ? acaoPaiUuid : event.oldData.acaoPaiUuid,
      status: status != null ? status : event.oldData.status,
      somentePrefeitura:
        somentePrefeitura != null
          ? somentePrefeitura
          : event.oldData.somentePrefeitura,
      telaInicial:
        telaInicial != null ? telaInicial : event.oldData.telaInicial,
      iconeTelaInicial:
        iconeTelaInicial != null
          ? iconeTelaInicial
          : event.oldData.iconeTelaInicial,
    }

    this.subscription = this.service
      .put(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'A ação ' + event.oldData.nome + ' foi atualizada.',
          })
          this.fetchGrid(this.formulario.get('modulo').value)
          this.dataTree.instance.cancelEditData()
        },
        (err: any) => {
          this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  public onRowRemoving(event: any): void {
    this.subscription = this.service.delete(event.data.uuid).subscribe(
      () => {
        this.toastr.send({
          success: true,
          message: 'Ação ' + event.data.nome + ' excluída com sucesso.',
        })
      },
      (resp: any) => this.toastr.bulkSend(resp.mensagens),
    )
  }

  public onRowRemoved(event: any): void {
    setTimeout(() => {
      this.fetchGrid(this.formulario.get('modulo').value)
    }, 200)
  }

  public bloquearFuncoes(block): void {
    this.bloqueia = block
  }

  private changeModule(): void {
    this.formulario
      .get('modulo')
      .valueChanges.pipe(distinctUntilChanged())
      .subscribe(uuid => {
        if (uuid) {
          this.fetchGrid(uuid)
          this.getActionsDad(uuid)
        } else {
          this.gridData = []
          this.actionsDad = []
        }
      })
  }
}
