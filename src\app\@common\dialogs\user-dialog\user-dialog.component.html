<eqp-nebular-dialog
  [dialogTitle]="dialogTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="true"
  [rightFirstButtonId]="'confirm-source-resource-selection'"
  [rightFirstButtonTitle]="
    grid?.instance?.getSelectedRowsData().length
      ? 'Selecionar'
      : 'É preciso selecionar uma licitação'
  "
  [rightFirstButtonDisabled]="!grid?.instance?.getSelectedRowsData().length"
  (rightFirstButtonEmitter)="confirm()"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonIconVisible]="true"
  [bottomLeftButtonId]="'dispose-source-resource-search'"
  [bottomLeftButtonTitle]="'Voltar'"
  [bottomLeftButtonDisabled]="false"
  (bottomLeftButtonEmitter)="dispose()"
>
  <ng-container>
    <dx-data-grid
      id="sourceResourceSearchGrid"
      [dataSource]="gridData"
      [allowColumnReordering]="true"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="true"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      [selectedRowKeys]="selected"
      keyExpr="uuid"
    >
      <dxo-selection mode="single"></dxo-selection>
      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager [showInfo]="true"> </dxo-pager>

      <dxo-header-filter [visible]="'showHeaderFilter'"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-group-panel [visible]="true" [emptyPanelText]="''"></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar"
      ></dxo-search-panel>

      <dxi-column
        *ngFor="let column of columnsTemplate"
        [dataField]="column?.dataField"
        [caption]="column?.caption"
        [hidingPriority]="column?.hidingPriority"
        [dataType]="column?.dataType"
        [sortOrder]="column?.sortOrder"
        [visible]="column?.visible"
        [cellTemplate]="column?.cellTemplate"
        [allowEditing]="column?.allowEditing"
        [allowExporting]="column?.allowExporting"
        [allowFiltering]="column?.allowFiltering"
        [allowHiding]="column?.allowHiding"
        [allowResizing]="column?.allowResizing"
        [allowSorting]="column?.allowSorting"
        [allowSearch]="column?.allowSearch"
        [allowGrouping]="column?.allowGrouping"
        [allowHeaderFiltering]="column?.allowHeaderFiltering"
        [allowReordering]="column?.allowReordering"
        [width]="column?.width"
        [alignment]="column?.alignment"
        [type]="column?.type"
      >
        <dxo-lookup
          *ngIf="column?.lookup"
          [dataSource]="column?.lookup?.dataSource"
          [displayExpr]="column?.lookup?.displayExpr"
          [valueExpr]="column?.lookup?.valueExpr"
        >
        </dxo-lookup>

        <dxi-button
          *ngFor="let button of column?.buttons"
          [name]="button?.name"
          [icon]="button?.icon"
          [cssClass]="button?.cssClass"
        ></dxi-button>

        <dxo-form-item
          *ngIf="column?.formItem"
          [colSpan]="column?.formItem?.colSpan"
          [editorOptions]="column?.formItem?.editorOptions"
          [editorType]="column?.formItem?.editorType"
          [isRequired]="column?.formItem?.isRequired"
          [visible]="column?.formItem?.visible"
        >
        </dxo-form-item>
      </dxi-column>
      <div
        *dxTemplate="let box of 'checkedTemplate'"
        [ngStyle]="{ 'pointer-events': 'none' }"
      >
        <dx-check-box
          *ngIf="isSelected(box.value)"
          [value]="isSelected(box.value)"
        ></dx-check-box>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-nebular-dialog>
