<div
  class="w-100 d-flex align-items-center flex-column flex-sm-row"
  style="max-width: 800px; gap: 1rem"
  [formGroup]="model"
>
  <div class="input-entity col-md-9">
    <eqp-nebular-select
      [size]="'small'"
      [shape]="'rectangle'"
      [dataSource]="entities"
      label="Entidade"
      valueExpr="uuid"
      displayExpr="nome"
      formControlName="entidadeUuid"
    ></eqp-nebular-select>
  </div>

  <div class="input-exercise col-md-3">
    <eqp-nebular-select
      [size]="'small'"
      [shape]="'rectangle'"
      [dataSource]="exercises"
      label="Exercício"
      valueExpr="uuid"
      displayExpr="exercicio"
      formControlName="exercicioUuid"
    ></eqp-nebular-select>
  </div>
</div>

<div class="container-socials">
  <span *ngIf="urlTransparencia">
    <a
      href="{{ urlTransparencia }}"
      target="_blank"
      title="Portal transparência"
    >
      <img
        nbTooltip="Ir para o portal"
        src="assets/icons-system/transparencia_portal.png"
        alt=""
      />
    </a>
  </span>
  <a href="http://www.equiplano.com.br" target="_blank"
    ><img
      nbTooltip="Equiplano"
      src="assets/img/equiplano-logo-vertical.png"
      alt=""
  /></a>
  <a
    *ngIf="config?.footer?.showLinkedin"
    href="{{ config?.footer?.linkedinLink }}"
    target="_blank"
  >
    <img nbTooltip="Linkedin" src="assets/icons/linkedin.png" alt="linkedin" />
  </a>
  <a
    *ngIf="config?.footer?.showInstagram"
    href="{{ config?.footer?.instagramLink }}"
    target="_blank"
  >
    <img
      nbTooltip="Instagram"
      src="assets/icons/instagram.png"
      alt="instagram"
    />
  </a>
  <a
    *ngIf="config?.footer?.showFacebook"
    href="{{ config?.footer?.facebookLink }}"
    target="_blank"
    ><img nbTooltip="Facebook" src="assets/icons/facebook.png" alt="facebook"
  /></a>
  <a
    *ngIf="config?.footer?.showTwitter"
    href="{{ config?.footer?.twitterLink }}"
    target="_blank"
    ><img nbTooltip="Twitter" src="assets/icons/twitter.png" alt="twitter"
  /></a>
</div>
