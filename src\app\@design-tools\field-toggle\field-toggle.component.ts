import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'eqp-field-toggle',
  templateUrl: './field-toggle.component.html',
  styleUrls: ['./field-toggle.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FieldToggleComponent),
      multi: true,
    },
  ],
})
export class FieldToggleComponent implements ControlValueAccessor, OnInit {
  @Input() tabIndex: number = 0;
  @Input() disabled = false;
  @Input() readonly = false;
  @Input() required = false;
  @Input() labelPosition: 'left' | 'right' | 'start' | 'end' = 'right';
  @Input() label = '';
  @Input() name = '';
  @Input() class = '';

  currentValue: boolean;

  onChanged(value?: any) {
    this.currentValue = value;
  }

  onTouched: (value?: any) => {};

  ngOnInit(): void {}

  set value(value: any) {
    this.currentValue = value == 'S';
  }

  changeValue(value: any) {
    const outValue = value ? 'S' : 'N';
    this.onChanged(outValue);
    this.onTouched();
  }

  writeValue(value: any) {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChanged = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean) {
    this.disabled = isDisabled;
  }

  public modelChanged(event: any): void {
    this.onChanged(event);
  }
}
