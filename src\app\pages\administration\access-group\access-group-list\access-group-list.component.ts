import { Component, OnDestroy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'

import { AccessGroupService } from '../access-group.service'

@Component({
  selector: 'eqp-access-group-list',
  templateUrl: './access-group-list.component.html',
  styleUrls: ['./access-group-list.component.scss'],
})
export class AccessGroupListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Controle de acesso | Grupo de acesso'

  public gridData: any
  public clienteList: any
  public moduloList: any

  private subscription: Subscription
  constructor(
    private service: AccessGroupService,
    private toastr: ToastrService,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
    this.permissao('/equiplano/controle-acesso/grupo-acesso')
  }

  public ngOnInit(): void {
    this.clienteList = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        'grupo_acesso/cliente',
        10,
      ),
      pageSize: 10,
      paginate: true,
    }
    this.moduloList = {
      store: this.service.getDataSourceFiltro(
        'uuid',
        'grupo_acesso/module',
        10,
      ),
      pageSize: 10,
      paginate: true,
    }
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      'grupo_acesso/lista-grid',
      10,
    )
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items[0].showText = 'ever'
    event.toolbarOptions.items[0].options.text = 'Novo grupo'
    event.toolbarOptions.items[0].options.hint = 'Novo grupo'

    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/controle-acesso/grupo-acesso/novo`])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([
      `equiplano/controle-acesso/grupo-acesso/edit/${uuid}`,
    ])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Grupo de acesso excluído com sucesso.',
              })
              this.gridData = this.service.getDataSourceFiltro(
                'uuid',
                'grupo_acesso',
                10,
              )
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public mostrarCliente(data): string {
    return data.data.clienteNome
  }

  public mostrarModulo(data): string {
    return data.data.moduloDescricao
  }
}
