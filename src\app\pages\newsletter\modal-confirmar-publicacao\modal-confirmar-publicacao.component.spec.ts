import { ComponentFixture, TestBed } from '@angular/core/testing'
import { NbDialogRef } from '@nebular/theme'

import { ModalConfirmarPublicacaoComponent } from './modal-confirmar-publicacao.component'

describe('ModalConfirmarPublicacaoComponent', () => {
  let component: ModalConfirmarPublicacaoComponent
  let fixture: ComponentFixture<ModalConfirmarPublicacaoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ModalConfirmarPublicacaoComponent],
      providers: [
        {
          provide: NbDialogRef,
          useValue: {
            close: jasmine.createSpy('close')
          }
        }
      ]
    }).compileComponents()

    fixture = TestBed.createComponent(ModalConfirmarPublicacaoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
