import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { EmailConfigListComponent } from '@pages/administration/email-config/email-config-list/email-config-list.component'

import { AdministrationComponent } from './administration.component'
import { EmailConfigComponent } from './email-config/email-config/email-config.component'
import { UserAccessLimitationFormComponent } from './user-access-limitation/user-access-limitation-form/user-access-limitation-form.component'
import { UserAccessLimitationListComponent } from './user-access-limitation/user-access-limitation-list/user-access-limitation-list.component'
import { UsersListComponent } from './users/users-list/users-list.component'
import { UsersComponent } from './users/users/users.component'

const routes: Routes = [
  {
    path: '',
    component: AdministrationComponent,
    children: [
      {
        path: 'usuarios',
        component: UsersListComponent,
      },
      {
        path: 'usuarios/novo',
        component: UsersComponent,
      },
      {
        path: 'usuarios/edit/:uuid',
        component: UsersComponent,
      },
      {
        path: 'configuracao-email',
        component: EmailConfigListComponent,
      },
      {
        path: 'configuracao-email/novo',
        component: EmailConfigComponent,
      },
      {
        path: 'configuracao-email/edit/:uuid',
        component: EmailConfigComponent,
      },
      {
        path: 'limitacao-acesso',
        component: UserAccessLimitationListComponent,
      },
      {
        path: 'limitacao-acesso/novo',
        component: UserAccessLimitationFormComponent,
      },
      {
        path: 'limitacao-acesso/edit/:uuid',
        component: UserAccessLimitationFormComponent,
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdministrationRoutingModule {}
