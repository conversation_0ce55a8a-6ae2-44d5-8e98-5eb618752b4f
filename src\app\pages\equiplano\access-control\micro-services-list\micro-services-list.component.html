<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="microServices.loading"
  [spinnerStatus]="'primary'"
  [topRightButtonVisible]="true"
  [topRightButtonIconVisible]="true"
  [topRightButtonIcon]="'fas fa-sync'"
  [topRightButtonId]="'update-micro-services-list'"
  [topRightButtonTitle]="'Atualizar'"
  (topRightButtonEmitter)="fetchGrid()"
>
  <ng-container>
    <eqp-micro-services #microServices></eqp-micro-services>
  </ng-container>
</eqp-standard-page>
