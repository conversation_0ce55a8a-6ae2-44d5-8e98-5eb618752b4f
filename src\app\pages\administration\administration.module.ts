import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import { EmailConfigListComponent } from '@pages/administration/email-config/email-config-list/email-config-list.component'
import { EmailConfigComponent } from '@pages/administration/email-config/email-config/email-config.component'
import {
  DxDataGridModule,
  DxDropDownBoxModule,
  DxSelectBoxModule,
} from 'devextreme-angular'

import { PermissaoListaComponent } from './access-group/permissao-lista/permissao-lista.component'
import { UsuarioListaComponent } from './access-group/usuario-lista/usuario-lista.component'
import { AdministrationRoutingModule } from './administration-routing.module'
import { AdministrationComponent } from './administration.component'
import { UserAccessLimitationFormComponent } from './user-access-limitation/user-access-limitation-form/user-access-limitation-form.component'
import { UserAccessLimitationListComponent } from './user-access-limitation/user-access-limitation-list/user-access-limitation-list.component'
import { AccessGroupComponent } from './users/access-group/access-group.component'
import { AnexoComponent } from './users/anexo/anexo.component'
import { GrupoAcessoListaComponent } from './users/grupo-acesso-lista/grupo-acesso-lista.component'
import { TokenComponent } from './users/token/token.component'
import { UsersListComponent } from './users/users-list/users-list.component'
import { UsersComponent } from './users/users/users.component'

@NgModule({
  declarations: [
    AdministrationComponent,
    EmailConfigComponent,
    EmailConfigListComponent,
    UsersComponent,
    UsersListComponent,
    AccessGroupComponent,
    AnexoComponent,
    TokenComponent,
    UserAccessLimitationListComponent,
    UserAccessLimitationFormComponent,
    GrupoAcessoListaComponent,
    UsuarioListaComponent,
    PermissaoListaComponent,
  ],
  imports: [
    CommonModule,
    AdministrationRoutingModule,
    CommonToolsModule,
    DxSelectBoxModule,
    DxDropDownBoxModule,
    DxDataGridModule,
  ],
  exports: [
    GrupoAcessoListaComponent,
    UsuarioListaComponent,
    PermissaoListaComponent,
  ],
})
export class AdministrationModule {}
