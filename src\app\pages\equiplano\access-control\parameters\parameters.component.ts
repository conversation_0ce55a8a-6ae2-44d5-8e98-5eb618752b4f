import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NebularSelect } from '@design-tools/interfaces/nebular-select'
import {
  MicroServicesReturnInterface,
} from '@pages/equiplano/access-control/micro-services/micro-service'
import {
  ParameterInterface,
  ParameterReturnInterface,
} from '@pages/equiplano/access-control/parameters/parameter'
import {
  ParametersService,
} from '@pages/equiplano/access-control/parameters/parameters.service'
import { ClientReturnInterface } from '@pages/equiplano/clients/clients'
import { ClientsService } from '@pages/equiplano/clients/clients.service'
import { DxDataGridComponent } from 'devextreme-angular'
import { Subscription } from 'rxjs'
import { finalize } from 'rxjs/operators'

import { ActionsReturnInterface } from '../actions/actions'
import { ActionsService } from './../actions/actions.service'
import {
  MicroServicesService,
} from './../micro-services/micro-services.service'

@Component({
  selector: 'eqp-parameters',
  templateUrl: './parameters.component.html',
  styleUrls: ['./parameters.component.scss'],
})
export class ParametersComponent implements OnInit, OnDestroy {
  public loading: boolean = false
  public gridData: ParameterInterface[] = []
  public columnsTemplate: DxColumnInterface[] = []
  public actions: NebularSelect[] = []
  public clients: NebularSelect[] = []
  public microServices: NebularSelect[] = []
  public verbs: NebularSelect[] = [
    { texto: 'Cadastrar', valor: 'INSERT' },
    { texto: 'Apagar', valor: 'DELETE' },
    { texto: 'Atualizar', valor: 'UPDATE' },
  ]
  public obrigatorio: NebularSelect[] = [
    { texto: 'Sim', valor: 'YES' },
    { texto: 'Não', valor: 'NO' },
  ]
  public types: NebularSelect[] = [
    { texto: 'Texto', valor: 'STRING' },
    { texto: 'Verdadeiro/Falso', valor: 'BOOLEAN' },
    { texto: 'Lista', valor: 'ARRAY' },
    { texto: 'Número inteiro', valor: 'INTEGER' },
    { texto: 'Objeto', valor: 'OBJECT' },
    { texto: 'Número decimal (DECIMAL)', valor: 'DECIMAL' },
    { texto: 'Número decimal (DOUBLE)', valor: 'DOUBLE' },
  ]

  private subscription: Subscription
  private actionsSubscription: Subscription
  private clientsSubscription: Subscription
  private microServicesSubscription: Subscription

  @ViewChild('dataGrid', { static: false }) dataGrid: DxDataGridComponent
  public bloqueia: boolean = false

  constructor(
    private service: ParametersService,
    private actionsService: ActionsService,
    private clientsService: ClientsService,
    private microServicesService: MicroServicesService,
    private toastr: ToastrService,
  ) {}

  public ngOnInit(): void {
    this.columnsTemplate = this.getColumnsTemplate()
    this.getActions()
    this.getClients()
    this.getMicroServices()
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.actionsSubscription) this.actionsSubscription.unsubscribe()
    if (this.clientsSubscription) this.clientsSubscription.unsubscribe()
    if (this.microServicesSubscription)
      this.microServicesSubscription.unsubscribe()
  }

  public fetchGrid(): void {
    this.loading = true
    this.subscription = this.service
      .get()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: ParameterReturnInterface) => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getActions(): void {
    this.loading = true
    this.actionsSubscription = this.actionsService
      .get()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: ActionsReturnInterface) => {
          data.dados.forEach(action => {
            this.actions.push({
              texto: action.nome,
              valor: action.uuid,
            })
          })
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getClients(): void {
    this.loading = true
    this.clientsSubscription = this.clientsService
      .get()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: ClientReturnInterface) => {
          data.dados.forEach(client => {
            this.clients.push({
              texto: client.nome,
              valor: client.uuid,
            })
          })
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getMicroServices(): void {
    this.loading = true
    this.microServicesSubscription = this.microServicesService
      .get()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: MicroServicesReturnInterface) => {
          data.dados.forEach(client => {
            this.microServices.push({
              texto: client.nome,
              valor: client.uuid,
            })
          })
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'uuid',
        visible: false,
        allowEditing: false,
        allowHiding: false,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Ação',
        dataField: 'verbo',
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Ação' } },
        },
        lookup: {
          dataSource: this.verbs,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Campo',
        dataField: 'campo',
        formItem: {
          isRequired: true,
          editorOptions: { maxLength: '255', inputAttr: { id: 'Campo' } },
        },
      },
      {
        caption: 'Função',
        dataField: 'funcao',
        formItem: {
          isRequired: true,
          editorOptions: { maxLength: '255', inputAttr: { id: 'Função' } },
        },
      },
      {
        caption: 'Obrigatoriedade',
        dataField: 'obrigatoriedade',
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Obrigatoriedade' } },
        },
        lookup: {
          dataSource: this.obrigatorio,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Tamanho',
        dataField: 'tamanho',
        formItem: {
          isRequired: true,
          editorOptions: { maxLength: '100', inputAttr: { id: 'Tamanho' } },
        },
      },
      {
        caption: 'Tipo',
        dataField: 'tipo',
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Tipo' } },
        },
        lookup: {
          dataSource: this.types,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Valor Padrão',
        dataField: 'valorPadrao',
        formItem: {
          isRequired: true,
          editorOptions: {
            maxLength: '255',
            inputAttr: { id: 'Valor Padrão' },
          },
        },
      },
      {
        caption: 'Menu',
        dataField: 'acaoUuid',
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Menu' } },
        },
        lookup: {
          dataSource: this.actions,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Cliente',
        dataField: 'clienteUuid',
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Cliente' } },
        },
        lookup: {
          dataSource: this.clients,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Micro serviço',
        dataField: 'microservicoUuid',
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Micro serviço' } },
        },
        lookup: {
          dataSource: this.microServices,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Ações',
        type: 'buttons',
        buttons: [
          {
            name: 'edit',
            icon: 'fas fa-edit',
          },
          {
            name: 'delete',
            icon: 'fas fa-trash-alt',
          },
        ],
      },
    ]
    return template
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items[1].showText = 'ever'
    event.toolbarOptions.items[1].options.text = 'Novo parâmetro'
    event.toolbarOptions.items[1].options.hint = 'Novo parâmetro'

    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public onRowinserting(event: any): void {
    this.loading = true
    const {
      verbo,
      campo,
      funcao,
      obrigatoriedade,
      tamanho,
      tipo,
      valorPadrao,
      clienteUuid,
      acaoUuid,
      microservicoUuid,
    } = event.data
    event.cancel = true

    const dto = {
      verbo,
      campo,
      funcao,
      obrigatoriedade,
      tamanho,
      tipo,
      valorPadrao,
      clienteUuid,
      acaoUuid,
      microservicoUuid,
    }

    this.subscription = this.service
      .post(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Parâmetro inserido com sucesso.',
          })
          this.fetchGrid()
          this.dataGrid.instance.cancelEditData()
        },
        (err: any) => {
          this.fetchGrid()
          this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  public onRowUpdating(event: any): void {
    this.loading = true
    const {
      verbo,
      campo,
      funcao,
      obrigatoriedade,
      tamanho,
      tipo,
      valorPadrao,
      clienteUuid,
      acaoUuid,
      microservicoUuid,
    } = event.newData
    event.cancel = true

    const dto = {
      uuid: event.oldData.uuid,
      verbo: verbo ? verbo : event.oldData.verbo,
      campo: campo ? campo : event.oldData.campo,
      funcao: funcao ? funcao : event.oldData.funcao,
      obrigatoriedade: obrigatoriedade
        ? obrigatoriedade
        : event.oldData.obrigatoriedade,
      tamanho: tamanho ? tamanho : event.oldData.tamanho,
      tipo: tipo ? tipo : event.oldData.tipo,
      valorPadrao: valorPadrao ? valorPadrao : event.oldData.valorPadrao,
      clienteUuid: clienteUuid ? clienteUuid : event.oldData.clienteUuid,
      acaoUuid: acaoUuid ? acaoUuid : event.oldData.acaoUuid,
      microservicoUuid: microservicoUuid
        ? microservicoUuid
        : event.oldData.microservicoUuid,
    }

    this.subscription = this.service
      .put(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'O parâmetro ' + event.oldData.nome + ' foi atualizado.',
          })
          this.fetchGrid()
          this.dataGrid.instance.cancelEditData()
        },
        (err: any) => {
          this.fetchGrid()
          this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  public onRowRemoving(event: any): void {
    this.subscription = this.service.delete(event.data.uuid).subscribe(
      () => {
        this.toastr.send({
          success: true,
          message: 'Parâmetro ' + event.data.nome + ' excluído com sucesso.',
        })
      },
      (resp: any) => this.toastr.bulkSend(resp.mensagens),
    )
  }

  public onRowRemoved(event: any): void {
    setTimeout(() => {
      this.fetchGrid()
    }, 200)
  }

  public bloquearFuncoes(block): void {
    this.bloqueia = block
  }
}
