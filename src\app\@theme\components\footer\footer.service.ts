import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'

@Injectable({
  providedIn: 'root',
})
export class FooterService {
  constructor(private _http: HttpClient) {}

  getDomain() {
    return this._http.get<any>(`transparencia/dominio`)
  }

  public atualizaSessao(sessao: any) {
    const headers = new HttpHeaders()

    return this._http.put<any>(`auth/sessao/${sessao.idToken}`, sessao, {
      headers,
    })
  }
}
