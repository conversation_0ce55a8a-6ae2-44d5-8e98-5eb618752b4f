import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormControl, FormGroup } from '@angular/forms'
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser'
import { ActivatedRoute, NavigationStart, Router } from '@angular/router'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService, NbThemeService } from '@nebular/theme'
import { Subscription } from 'rxjs'
import { debounceTime, distinctUntilChanged, finalize, first } from 'rxjs/operators'

import { UserDataService } from '@common/services/user-data.service'
import { EsqueciSenhaComponent } from '../esqueci-senha/esqueci-senha.component'
import { SenhaComponent } from '../senha/senha.component'
import { CrossStorageService } from '../services/cross-storage.service'
import { LoginService } from './../services/login.service'

@Component({
  selector: 'eqp-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit, OnDestroy {
  public loginForm: FormGroup
  public loading = false
  public authenticated = false
  public entitySelected = false
  public municipioSelected = false
  public returnUrl: string
  private previousUrl: string
  public error = ''
  private subs: Subscription
  private subsTwo: Subscription

  public municipios: any[] = []
  public entities: any[] = []
  public entity: any
  public exercises: any[] = []

  public defaultEye: boolean = false
  public classEye: string = 'fas fa-eye'
  public typeText: string = 'password'
  public toolTipText: string = 'Mostrar senha'
  private errroSenha: string = ''
  // Adicionar novas propriedades para a pesquisa de clientes/municípios
  searchMunicipioControl = new FormControl('');
  filteredMunicipios: any[] = [];
  private searchMunicipioSubscription: Subscription;

  // Adicionar novas propriedades para a pesquisa de entidades
  searchEntityControl = new FormControl('');
  filteredEntities: any[] = [];
  private searchEntitySubscription: Subscription;

  constructor(
    private service: LoginService,
    private themeService: NbThemeService,
    private route: ActivatedRoute,
    private router: Router,
    private crossStorageService: CrossStorageService,
    private toastr: ToastrService,
    private domSanitizer: DomSanitizer,
    private userData: UserDataService,
    private dialogService: NbDialogService,
  ) {
    this.subsTwo = this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        this.previousUrl = this.router.url
        window.localStorage.setItem('previousUrl', this.router.url)
      }
    })
  }

  public ngOnInit(): void {
    let theme = this.userData.getTheme()
    if (!theme) theme = 'default'
    this.themeService.changeTheme(theme)
    this.loginForm = this.getNewModel()

    this.manageUrls()
    const mensagem = JSON.parse(localStorage.getItem('error'))
    localStorage.removeItem('error')
    localStorage.removeItem('userData')
    if (mensagem) {
      setTimeout(() => {
        this.toastr.bulkSend(mensagem)
      }, 0)
    }

    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) {
        this.loading = true
        this.service
          .resetarSenha(uuid)
          .pipe(first())
          .subscribe(dados => {
            const error = [
              {
                success:
                  dados.body.dados ===
                  'Senha alterada com sucesso, verifique seu e-mail',
                error:
                  dados.body.dados !==
                  'Senha alterada com sucesso, verifique seu e-mail',
                message: dados.body.dados,
              },
            ]

            localStorage.setItem('error', JSON.stringify(error))
            this.router.navigate(['login'])
          })
      }
    })

    this.configurarPesquisaEntidade();
    this.configurarPesquisaMunicipio();
  }

  public ngOnDestroy(): void {
    if (this.subs) this.subs.unsubscribe()
    if (this.subsTwo) this.subsTwo.unsubscribe()
    if (!this.entity) {
      this.service.logout()
      this.router.navigate(['/login'])
    }
    if(this.searchMunicipioSubscription) {
      this.searchMunicipioSubscription.unsubscribe();
    }
    if(this.searchEntitySubscription) {
      this.searchEntitySubscription.unsubscribe();
    }
  }

  private getNewModel(): FormGroup {
    return new FormGroup({
      username: new FormControl(),
      password: new FormControl(),
      exercise: new FormControl(),
    })
  }

  private getMunicipios(): void {
    this.loading = true
    this.service
      .getMunicipios()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: any) => {
          this.municipios = data.data
          this.filteredMunicipios = [...data.data]
          if (this.municipios.length === 1) {
            this.selectMunicipio(this.municipios[0])
          }
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getEntidades(): void {
    this.loading = true
    this.service
      .entidades()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: any) => {
          this.entities = data.data
          this.filteredEntities = [...data.data]
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private manageUrls(): void {
    const returnUrl = this.route.snapshot.queryParams['returnUrl']
    const wayBack = this.route.snapshot.queryParams['wayBack']

    if (wayBack && wayBack.indexOf('/login') === -1) {
      this.crossStorageService.wayBack = wayBack
    }

    if (returnUrl && returnUrl.indexOf('/login') === -1) {
      this.returnUrl = returnUrl
    } else {
      this.returnUrl = '/'
    }
  }
  // convenience getter for easy access to form fields
  get f(): any {
    return this.loginForm.controls
  }

  public onSubmit(): void {
    // stop here if form is invalid
    if (this.loginForm.invalid) {
      return
    }

    const payload = {
      identificador: this.f.username.value,
      senha: this.f.password.value,
    }

    this.loading = true
    this.service
      .login(payload)
      .pipe(first())
      .subscribe(
        (data: any) => {
          this.errroSenha = data.body.mensagemErro
          this.authenticated = true
          this.getMunicipios()
        },
        (err: any) => {
          this.loading = false
        },
      )
  }

  public alterPassword(): void {
    const payload = {
      identificador: this.f.username.value,
      senha: this.f.password.value,
    }
    const dialogRef = this.dialogService.open(SenhaComponent, {
      context: {
        dados: {
          payload,
        },
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        this.toastr.send({
          success: true,
          message: 'A senha foi alterada com sucesso.',
        })
        this.getEntidades()
        this.authenticated = false
        this.municipioSelected = true
      }
    })
  }

  public esqueciSenha(): void {
    const dialogRef = this.dialogService.open(EsqueciSenhaComponent, {
      context: {
        usuario: this.loginForm.get('username').value,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {})
  }

  public selectMunicipio(municipio: any): void {
    const userData = JSON.parse(localStorage.getItem('userData'))
    localStorage.removeItem('userData')
    userData.municipioClienteUuid = municipio.uuid
    localStorage.setItem('userData', JSON.stringify(userData))

    this.service
      .atualizarToken({
        tokenUuid: userData.tokenUuid,
        municipioUuid: userData.municipioClienteUuid,
      })
      .pipe(first())
      .subscribe(token => {
        const userDataToken = JSON.parse(localStorage.getItem('userData'))
        localStorage.removeItem('userData')
        userDataToken.tokenJwt = token.dados
        localStorage.setItem('userData', JSON.stringify(userDataToken))
        if (this.errroSenha === 'Senha expirada') {
          this.toastr.send({
            error: true,
            message: this.errroSenha,
          })

          this.alterPassword()
        } else {
          this.getEntidades()
          this.authenticated = false
          this.municipioSelected = true
        }
      })
  }

  public selectEntity(entity: any): void {
    const userData = JSON.parse(localStorage.getItem('userData'))
    localStorage.removeItem('userData')

    userData.entidadeUuid = entity.uuid
    userData.clienteUuid = entity.clienteUuid
    userData.entidade = entity
    localStorage.setItem('userData', JSON.stringify(userData))
    this.entity = entity

    this.service
      .atualizarToken({
        tokenUuid: userData.tokenUuid,
        entidadeUuid: userData.entidadeUuid,
        entidadeNome: userData.entidade.nome,
        clienteUuid: userData.clienteUuid,
      })
      .pipe(first())
      .subscribe(token => {
        const userDataToken = JSON.parse(localStorage.getItem('userData'))
        localStorage.removeItem('userData')
        userDataToken.tokenJwt = token.dados
        localStorage.setItem('userData', JSON.stringify(userDataToken))
        this.service
          .exercicios()
          .pipe(first())
          .subscribe(data => {
            this.exercises = []
            const exercicioStatusExecucao = 4 // Código de status do exercício correspondente a fase "Execução"
            const exercicioEmExecucao = data.data
              .filter(item => item.exercicioStatus.codigo === exercicioStatusExecucao)
              .sort((a, b) => b.exercicio - a.exercicio)[0]; // pega o maior exercício com status "Execução"

            data.data.sort(function (a, b) {
              return a.exercicio > b.exercicio
                ? -1
                : a.exercicio < b.exercicio
                ? 1
                : 0
            })
            data.data.forEach(item => {
              if (item.exercicio >= 2013) {
                this.exercises.push({
                  texto: item.exercicio,
                  valor: item.uuid,
                  status: item.exercicioStatus,
                })
              }
            })

            if (exercicioEmExecucao) {
              this.loginForm.get('exercise').patchValue(exercicioEmExecucao.uuid)
            }
            this.municipioSelected = false
            this.entitySelected = true
          })
      })
  }

  public selectExercise(): void {
    const user = JSON.parse(localStorage.getItem('userData'))
    localStorage.removeItem('userData')

    user.exercicioUuid = this.f.exercise.value
    this.exercises.forEach(item => {
      if (user.exercicioUuid === item.valor) {
        user.exercicio = item.texto
        user.exercise = item
      }
    })
    localStorage.setItem('userData', JSON.stringify(user))

    this.service
      .atualizarToken({
        tokenUuid: user.tokenUuid,
        exercicioUuid: user.exercicioUuid,
        exercicioAno: user.exercicio,
      })
      .pipe(first())
      .subscribe(token => {
        const userDataoken = JSON.parse(localStorage.getItem('userData'))
        localStorage.removeItem('userData')
        userDataoken.tokenJwt = token.dados
        localStorage.setItem('userData', JSON.stringify(userDataoken))

        this.service
          .gravarSessao(userDataoken)
          .pipe(first())
          .subscribe(sessao => {
            localStorage.removeItem('userData')
            localStorage.setItem('userData', JSON.stringify(sessao.dados))

            setTimeout(() => {
              if (this.crossStorageService.wayBack) {
                window.open(
                  `${this.crossStorageService.wayBack}?idToken=${sessao.dados.idToken}`,
                  '_self',
                )
                return
              }
              this.router.navigate([this.returnUrl])
            }, 100)
          })
      })
  }

  public getBrasao(brasao: string): SafeResourceUrl {
    return this.domSanitizer.bypassSecurityTrustResourceUrl(
      `data:image/png;base64,${brasao}`,
    )
  }

  public back(): void {
    if (this.authenticated) {
      this.authenticated = false
      this.municipios = []
    }
    if (this.municipioSelected) {
      this.authenticated = true
      this.municipioSelected = false
      this.entities = []
    }
    if (this.entitySelected) {
      this.municipioSelected = true
      this.entitySelected = false
      this.exercises = []
    }
  }

  public toggleDefault(): void {
    this.defaultEye = !this.defaultEye
    if (this.defaultEye) {
      this.classEye = 'fas fa-eye-slash'
      this.typeText = 'text'
      this.toolTipText = 'Ocultar senha'
    } else {
      this.classEye = 'fas fa-eye'
      this.typeText = 'password'
      this.toolTipText = 'Mostrar senha'
    }
  }

  public loginCertificado(): void {
    this.service
    .getCertificado()
    .pipe(first())
    .subscribe(
      (data: String) => {
         
        this.service.loginCertificado(data)
          .pipe(first())
          .subscribe(
            (data: any) => {
              this.errroSenha = data.body.mensagemErro
              this.authenticated = true
              this.getMunicipios()
            },
            (err: any) => {
              this.loading = false
            },
          )
          
        }
      )
  }

  private configurarPesquisaMunicipio() {
    this.searchMunicipioSubscription = this.searchMunicipioControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(searchTerm => {
        this.filterMunicipios(searchTerm);
      });
  }

  filterMunicipios(searchTerm: string) {
    if (!searchTerm) {
      this.filteredMunicipios = [...this.municipios];
      return;
    }

    searchTerm = searchTerm.toLowerCase();
    this.filteredMunicipios = this.municipios.filter(municipio => 
      municipio.nome.toLowerCase().includes(searchTerm)
    );
  }

  clearMunicipioSearch() {
    this.searchMunicipioControl.setValue('');
  }

  private configurarPesquisaEntidade() {
    this.searchEntitySubscription = this.searchEntityControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(searchTerm => {
        this.filterEntities(searchTerm);
      });
  }

  filterEntities(searchTerm: string) {
    if (!searchTerm) {
      this.filteredEntities = [...this.entities];
      return;
    }

    searchTerm = searchTerm.toLowerCase();
    this.filteredEntities = this.entities.filter(entity => 
      entity.nome?.toLowerCase().includes(searchTerm) ||
      (entity.codigo?.toString() || '').includes(searchTerm)
    );
  }

  clearEntitySearch() {
    this.searchEntityControl.setValue('');
  }
}
