import { Component, Input, OnInit } from '@angular/core'
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser'
import { SanitizePipe } from '@common/pipes/sanitize/sanitize.pipe'
import { ModulesInterface } from '@pages/equiplano/access-control/modules/modules'
import { ModulesService } from '@pages/equiplano/access-control/modules/modules.service'
import { first } from 'rxjs/operators'

@Component({
  selector: 'eqp-system-container',
  templateUrl: './system-container.component.html',
  styleUrls: ['./system-container.component.scss'],
})
export class SystemContainerComponent implements OnInit {
  @Input() public containerTitle: string

  public modules: ModulesInterface[] = []

  constructor(
    private modulesService: ModulesService,
    private domSanitizer: DomSanitizer,
    private sanitize: SanitizePipe,
  ) {}

  public ngOnInit(): void {
    this.getModules()
  }

  private getModules(): void {
    this.modulesService
      .get()
      .pipe(first())
      .subscribe(data => {
        for (const module of data.data) {
          if (module.sistema !== '45f7bc3d-3aaa-2082-0abd-dd6ff6b063c1') {
            this.modules.push(module)
          }
        }
      })
  }

  public switchModules(module: ModulesInterface): void {
    window.open(module.link, '_self')
  }

  public getLogoBase64(base64: string): SafeResourceUrl {
    return this.domSanitizer.bypassSecurityTrustResourceUrl(
      `data:image/png;base64,${base64}`,
    )
  }
}
