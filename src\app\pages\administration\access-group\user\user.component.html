<dx-data-grid
  id="usersGrid"
  [dataSource]="gridData"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [loadPanel]="loading"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [columnHidingEnabled]="true"
  [remoteOperations]="true"
  keyExpr="userUuid"
  (onToolbarPreparing)="onToolbarPreparing($event)"
  (onInitNewRow)="novoRegistro()"
>
  <dxo-export
    [enabled]="true"
    [excelWrapTextEnabled]="true"
    [excelFilterEnabled]="true"
    fileName="Controle de acesso | Usuários"
  ></dxo-export>

  <dxo-paging [pageSize]="10"></dxo-paging>
  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <dxo-filter-row [visible]="true"></dxo-filter-row>

  <dxo-sorting mode="multiple"></dxo-sorting>
  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar usuário"
  ></dxo-search-panel>

  <!-- <dxo-load-panel [enabled]="loading">
  </dxo-load-panel> -->

  <dxo-editing
    mode="form"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="true"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column dataField="usuarioNome" caption="Usuário"></dxi-column>

  <dxi-column
    dataField="uuid"
    caption=""
    [width]="80"
    [allowFiltering]="false"
    [allowSorting]="false"
    cellTemplate="acaoColumn"
  ></dxi-column>
  <div *dxTemplate="let data of 'acaoColumn'">
    <a
      *ngIf="nivelPermissao === 'FULL'"
      (click)="remover(data.value)"
      class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
    >
    </a>
  </div>
</dx-data-grid>
