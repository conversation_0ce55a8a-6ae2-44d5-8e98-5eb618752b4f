import { Component, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { CrudService } from '@common/services/crud.service';
import { DxColumnInterface } from '@design-tools/interfaces/column-interface';
import { NbDialogRef } from '@nebular/theme';
import { DxDataGridComponent } from 'devextreme-angular';
import { Subscription } from 'rxjs';

@Component({
  selector: 'eqp-user-dialog',
  templateUrl: './user-dialog.component.html',
  styleUrls: ['./user-dialog.component.scss']
})
export class UserDialogComponent implements OnInit, OnDestroy {
  public loading: boolean = false;
  @Input() uri = 'usuario';

  @Input()
  public dialogTitle: string = 'Selecionar | Usuário';
  public gridData: any;
  public columnsTemplate: DxColumnInterface[] = [];
  public selected: any[] = [];

  private subscription: Subscription;

  @ViewChild(DxDataGridComponent, { static: false })
  public grid: DxDataGridComponent;

  constructor(
    protected dialogRef: NbDialogRef<any>,
    private service: CrudService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid();
    this.columnsTemplate = this.getColumnsTemplate();
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe();
  }

  private fetchGrid(): void {
    this.gridData = {
      store: this.service.getDataSourceFiltro('uuid', this.uri, 10),
      paginate: true,
      pageSize: 10,
    };
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Nome',
        dataField: 'nome',
      },
      {
        caption: 'E-mail',
        dataField: 'email',
      },
      {
        caption: 'CPF',
        dataField: 'cpf',
      },
      {
        caption: 'Limitar Acesso',
        dataField: 'limitacaoAcesso',
      },
    ];
    return template;
  }

  public isSelected(uuid: any): boolean {
    if (this.grid.instance.getSelectedRowsData()[0]) {
      if (this.grid.instance.getSelectedRowsData()[0].uuid === uuid)
        return true;
    } else false;
  }

  public confirm(): void {
    const item = this.grid.instance.getSelectedRowsData()[0];
    this.dialogRef.close(item);
  }

  public dispose(): void {
    this.dialogRef.close(false);
  }
}
