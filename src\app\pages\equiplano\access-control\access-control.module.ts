import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import { NbInputModule, NbSelectModule } from '@nebular/theme'
import { DxSelectBoxModule, DxTreeListModule } from 'devextreme-angular'

import {
  AccessGroupComponent,
} from '../../administration/access-group/access-group/access-group.component'
import {
  AccessGroupListComponent,
} from './../../administration/access-group/access-group-list/access-group-list.component'
import {
  PermissionsComponent,
} from './../../administration/access-group/permissions/permissions.component'
import {
  UserComponent,
} from './../../administration/access-group/user/user.component'
import { AccessControlRoutingModule } from './access-control-routing.module'
import { AccessControlComponent } from './access-control.component'
import { ActionsListComponent } from './actions-list/actions-list.component'
import { ActionsComponent } from './actions/actions.component'
import {
  LicensesListComponent,
} from './licenses/licenses-list/licenses-list.component'
import { LicensesComponent } from './licenses/licenses/licenses.component'
import {
  MicroServicesListComponent,
} from './micro-services-list/micro-services-list.component'
import {
  MicroServicesComponent,
} from './micro-services/micro-services.component'
import {
  ModulesListComponent,
} from './modules/modules-list/modules-list.component'
import { ModulesComponent } from './modules/modules/modules.component'
import {
  ParametersListComponent,
} from './parameters-list/parameters-list.component'
import { ParametersComponent } from './parameters/parameters.component'

@NgModule({
  declarations: [
    AccessControlComponent,
    ModulesComponent,
    ModulesListComponent,
    ActionsComponent,
    ActionsListComponent,
    LicensesComponent,
    LicensesListComponent,
    MicroServicesComponent,
    MicroServicesListComponent,
    AccessGroupComponent,
    AccessGroupListComponent,
    ParametersComponent,
    ParametersListComponent,
    PermissionsComponent,
    UserComponent,
  ],
  imports: [
    CommonModule,
    AccessControlRoutingModule,
    CommonToolsModule,
    DxTreeListModule,
    DxSelectBoxModule,
    NbInputModule,
    NbSelectModule,
  ],
})
export class AccessControlModule {}
