import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { LoginService } from '../services/login.service';
import { ToastrService } from '@common/services/toastr/toastr.service';

@Injectable({
  providedIn: 'root'
})
export class UsuarioEquiplanoGuard implements CanActivate {
  constructor(
    private loginService: LoginService, 
    private toastr: ToastrService,
    private router: Router,
  ) {
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
      this.loginService.isUsuarioEquiplano().subscribe(isUsuarioEquiplano => {
        if(isUsuarioEquiplano) return true
        else {
          this.toastr.send({
            error: true,
            message: 'O acesso a essa rotina é permitido apenas para usuários Equiplano.',
          })
          this.router.navigate([''])
          return false
        }
      })
    return true;
  }
  
}
