<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
    <div class="mt-2">
      <eqp-breadcrumb></eqp-breadcrumb>
    </div>
  </nb-card-header>
  <nb-card-body>
    <ng-container [formGroup]="form">
      <div class="row pb-3">
        <div class="col-md-12">
          <eqp-nebular-search-field
            label="Usuário"
            codeLabel="CPF"
            codeKey="cpf"
            maxlength="11"
            formControlName="usuarioUuid"
            (onButtonClick)="onUserSearchDialog()"
            (onInputChange)="onUserSearchInput($event)"
          ></eqp-nebular-search-field>
        </div>
      </div>
      <div class="row pb-3">
        <div class="col-md-6 col-sm-12">
          <eqp-nebular-select
            label="Órgão"
            [dataSource]="orgao"
            valueExpr="uuid"
            displayExpr="nome"
            formControlName="orgaoUuid"
          ></eqp-nebular-select>
        </div>
        <div class="col-md-6 col-sm-12">
          <eqp-nebular-select
            label="Unidade"
            [dataSource]="unidade"
            [disabled]="!form.get('orgaoUuid').value"
            valueExpr="uuid"
            displayExpr="nome"
            formControlName="unidadeUuid"
          ></eqp-nebular-select>
        </div>
      </div>
    </ng-container>
  </nb-card-body>
  <nb-card-footer>
    <div class="d-flex justify-content-between">
      <button
        type="button"
        class="btn btn-dark"
        (click)="cancel()"
      >Voltar</button>
      <button
        type="button"
        class="btn btn-success"
        [disabled]="!form.valid"
        (click)="submit(form)"
      >Confirmar</button>
    </div>
  </nb-card-footer>
</nb-card>
