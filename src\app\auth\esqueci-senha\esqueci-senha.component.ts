import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { first } from 'rxjs/operators'
import { LoginService } from '../services/login.service'

@Component({
  selector: 'eqp-senha',
  templateUrl: './esqueci-senha.component.html',
  styleUrls: ['./esqueci-senha.component.scss'],
})
export class EsqueciSenhaComponent implements OnInit {
  @Input()
  public usuario: string

  public model: FormGroup

  constructor(
    protected ref: NbDialogRef<EsqueciSenhaComponent>,
    private builder: FormBuilder,
    private service: LoginService,
    private toastr: ToastrService,
  ) {}

  public ngOnInit(): void {
    this.model = this.getNewModel()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      username: [this.usuario, Validators.required],
    })
  }

  public enviarEmail(): void {
    if (this.model.invalid) {
      this.model.markAllAsTouched()
    } else {
      this.service
        .esqueciSenha(this.model.get('username').value)
        .pipe(first())
        .subscribe(dados => {
          this.toastr.send({
            success:
              dados.body.dados ===
              'Um e-mail foi enviado para redefinição da senha',
            error:
              dados.body.dados !==
              'Um e-mail foi enviado para redefinição da senha',
            message: dados.body.dados,
          })
          this.ref.close()
        })
    }
  }

  public fecharEsqueciSenha(): void {
    this.ref.close()
  }
}
