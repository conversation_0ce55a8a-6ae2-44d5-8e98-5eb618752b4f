import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { NewsletterInfoComponent } from './newsletter-info.component'
import { NewsletterViewDescriptionComponent } from './helper/newsletter-view-description/newsletter-view-description.component'
import { NewsletterViewMessageComponent } from './helper/newsletter-view-message/newsletter-view-message.component'
import { CommonToolsModule } from '@common/common-tools.module'

@NgModule({
  declarations: [
    NewsletterInfoComponent,
    NewsletterViewDescriptionComponent,
    NewsletterViewMessageComponent,
  ],
  imports: [CommonModule, CommonToolsModule],
})
export class NewsletterInfoModule {}