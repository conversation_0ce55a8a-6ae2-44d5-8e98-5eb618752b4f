import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import {
  ModalConfirmarComponent,
} from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { getMomentDate, getParsedDate } from '@common/helpers/parsers'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import {
  LicensesService,
} from '@pages/equiplano/access-control/licenses/licenses.service'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'

import {
  BaseTelasComponent,
} from './../../../../../@common/misc/base-telas.component'

@Component({
  selector: 'eqp-licenses',
  templateUrl: './licenses.component.html',
  styleUrls: ['./licenses.component.scss'],
})
export class LicensesComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Controle de acesso | Licenças'
  public formulario: FormGroup

  public modules: any
  public clients: any

  constructor(
    private service: LicensesService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
  }

  private loadSelects(): void {
    this.clients = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'licenca/cliente', 10),
      paginate: true,
      pageSize: 10,
    })
    this.modules = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'licenca/modulos', 10),
      paginate: true,
      pageSize: 10,
    })
  }

  public ngOnDestroy(): void {}

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      licenca: this.formBuilder.group({
        uuid: [''],
        expiraEm: ['', Validators.required],
        moduloUuid: ['', Validators.required],
        clienteUuid: ['', Validators.required],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) this.buscar(uuid)
      else {
        this.loadSelects()
      }
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        data.dados.expiraEm = getMomentDate(data.dados.expiraEm)

        this.formulario.get('licenca').patchValue(data.dados)
        this.loadSelects()
      })
  }

  public cancelar(): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/controle-acesso/licencas`])
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.loading = true
        this.service
          .delete(this.formulario.get('licenca.uuid').value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Licença excluída com sucesso.',
              })
              this.cancelar()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Falta preencher os campos obrigatórios',
      })
      this.formulario.markAllAsTouched()
    } else {
      this.loading = true
      if (this.formulario.get('licenca.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getLicencaDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Licença criada com sucesso.',
        })
        this.cancelar()
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getLicencaDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Licença atualizada com sucesso.',
        })
        this.cancelar()
      })
  }

  private getLicencaDto(): any {
    const dto = this.formulario.getRawValue()
    if (dto.licenca.expiraEm)
      dto.licenca.expiraEm = getParsedDate(dto.licenca.expiraEm)

    return dto.licenca
  }
}
