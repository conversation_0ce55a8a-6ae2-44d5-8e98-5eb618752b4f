<nb-card>
  <nb-card-header>Resultad<PERSON> da pesquisa</nb-card-header>
  <nb-card-body>
    <div class="row overflow-y">
      <div
        *ngFor="let item of result"
        class="col-md-12 pointer d-flex mb-2"
        title="{{ item.title }}"
      >
        <a (click)="goToSelectedItem(item.link)" class="ml-2 mr-2">
          <eqp-breadcrumb
            [fontSize]="14"
            [showInitLink]="false"
            [breadcrumbInfo]="{ manually: true, path: item.link }"
          ></eqp-breadcrumb>
        </a>
      </div>
      <ng-container>
        <div *ngIf="result.length <= 0" class="row">
          <div class="col-md-12">
            Nenhum resultado encontrado para "{{ searchTerm }}".
          </div>
        </div>
      </ng-container>
    </div>
  </nb-card-body>
</nb-card>
