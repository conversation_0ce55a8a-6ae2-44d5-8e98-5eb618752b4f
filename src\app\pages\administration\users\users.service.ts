import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import {
  UserInterface,
  UserReturnInterface,
} from '@pages/administration/users/user'
import { ClientReturnInterface } from '@pages/equiplano/clients/clients'
import { Observable } from 'rxjs'

import { CriptService } from '../../../auth/login/cript.service'
import { AccessGroupReturnInterface } from '../access-group/access-group'

@Injectable({
  providedIn: 'root',
})
export class UsersService extends CrudService {
  constructor(private http: HttpClient, private service: CriptService) {
    super(http)
  }

  public get(): Observable<UserReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<UserReturnInterface>(`usuario`, {
      headers,
    })
  }

  public getClientes(): Observable<ClientReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ClientReturnInterface>('usuario/cliente', {
      headers,
    })
  }

  public getIndividual(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`usuario/${uuid}`, {
      headers,
    })
  }

  public getGrupoAcesso(uuid: string): Observable<AccessGroupReturnInterface> {
    const headers = new HttpHeaders()

    return this.http.get<AccessGroupReturnInterface>(
      `usuario/grupo-acesso/${uuid}`,
      {
        headers,
      },
    )
  }

  public put(dto: UserInterface): Observable<any> {
    const json = JSON.stringify(dto)

    const key = {
      chaveAleatoria: this.service.encrypt(json),
      chaveGerada: this.service.encrypt(json),
      chaveHash: this.service.encrypt(json),
      chaveAcesso: this.service.encrypt(json),
      chave: this.service.encrypt(json),
      keyRandom: this.service.encrypt(json),
      keyGenerate: this.service.encrypt(json),
      keyHash: this.service.encrypt(json),
      keyAcess: this.service.encrypt(json),
      key: this.service.encryptString(json),
    }

    const headers = new HttpHeaders()

    return this.http.put<UserInterface>(`usuario/${dto.uuid}`, key, {
      headers,
      observe: 'response',
    })
  }

  public post(dto: UserInterface): Observable<any> {
    const json = JSON.stringify(dto)

    const key = {
      chaveAleatoria: this.service.encrypt(json),
      chaveGerada: this.service.encrypt(json),
      chaveHash: this.service.encrypt(json),
      chaveAcesso: this.service.encrypt(json),
      chave: this.service.encrypt(json),
      keyRandom: this.service.encrypt(json),
      keyGenerate: this.service.encrypt(json),
      keyHash: this.service.encrypt(json),
      keyAcess: this.service.encrypt(json),
      key: this.service.encryptString(json),
    }

    const headers = new HttpHeaders()

    return this.http.post<UserInterface>(`usuario`, key, {
      headers,
      observe: 'response',
    })
  }

  public postGrupoAcesso(
    userUuid: string,
    gruposAcessoUuid: string[],
  ): Observable<any> {
    const headers = new HttpHeaders()
    const dto = {
      usuarioUuid: userUuid,
      gruposAcessoUuid: gruposAcessoUuid,
    }
    return this.http.post<any>(`usuario/grupo-acesso`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: UserInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<UserInterface[]>(`usuario/lote`, batch, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: String): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`usuario/${uuid}`, {
      headers,
    })
  }

  public deleteGrupoAcesso(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`usuario/usuario-grupo-acesso/${uuid}`, {
      headers,
    })
  }

  public getCmbGrupoAcesso(): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`usuario/grupo-acesso/listagem-grid?take=0`, {
      headers,
    })
  }

  userAceessLimitation(userUuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`limitacao_acesso_orgao/${userUuid}`, {
      headers,
    })
  }

  createUserAceessLimitation(dto: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<any>('limitacao_acesso_orgao', dto, {
      headers,
    })
  }

  updateUserAceessLimitation(dto: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<any>(`limitacao_acesso_orgao/${dto.uuid}`, {
      headers,
    })
  }

  deleteUserAceessLimitation(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`limitacao_acesso_orgao/${uuid}`, {
      headers,
    })
  }

  public getAdminEqp(): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.get<any>(`usuario/admin-equiplano`, {
      headers,
    })
  }
}
