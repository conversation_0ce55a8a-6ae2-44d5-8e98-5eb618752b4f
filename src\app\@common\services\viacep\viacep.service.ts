import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Observable } from 'rxjs'

import { ViaCepReturnInterface } from './viacep'

@Injectable({
  providedIn: 'root',
})
export class ViaCepService {
  constructor(private http: HttpClient) {}

  public get(cep: string): Observable<ViaCepReturnInterface> {
    const headers = new HttpHeaders()

    return this.http.get<ViaCepReturnInterface>(`viacep/${cep}`, {
      headers,
    })
  }
}
