import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { AccessGroupService } from '../access-group.service'

@Component({
  selector: 'eqp-permissao-lista',
  templateUrl: './permissao-lista.component.html',
  styleUrls: ['./permissao-lista.component.scss'],
})
export class PermissaoListaComponent implements OnInit {
  @Input()
  public grupoAcesso: any
  public gridData: any
  selectedItemKeys: string[] = []
  public nivelAcessoData: any
  public nivelValor: string
  public formulario: FormGroup

  public constructor(
    protected ref: NbDialogRef<PermissaoListaComponent>,
    private service: AccessGroupService,
    private builder: FormBuilder,
    private toastr: ToastrService,
  ) {}

  ngOnInit(): void {
    this.formulario = this.builder.group({
      nivelAcesso: ['', [Validators.required]],
    })
    this.fetchGrid()
  }

  private fetchGrid(): void {
    this.nivelAcessoData = new DataSource({
      store: [
        { nome: 'Visualizador', valor: 'VIEWER' },
        { nome: 'Editor', valor: 'EDITOR' },
        { nome: 'Todos os Acessos', valor: 'FULL' },
      ],
      paginate: true,
      pageSize: 10,
    })
    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltroComposto(
        'uuid',
        `grupo_acesso/${this.grupoAcesso.moduloUuid}/acao`,
        10,
        '["moduloUuid","=","' +
          this.grupoAcesso.moduloUuid +
          '"],"and",["rota","startswith","/"]',
      ),
      map: (item: any) => {
        console.log(item)
        item.data = item.dados
        return item
      },
      paginate: true,
      pageSize: 10,
    })
  }

  public dismiss(): void {
    this.ref.close([])
  }

  onSelectionChanged({ selectedRowKeys }: any): void {
    this.selectedItemKeys = selectedRowKeys
  }

  public selecionar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Falta preencher os campos obrigatórios',
      })
      this.formulario.markAllAsTouched()
    } else {
      let permissao = {
        acoesUuid: this.selectedItemKeys,
        nivelAcesso: this.formulario.get('nivelAcesso').value,
        grupoAcessoUuid: this.grupoAcesso.uuid,
      }
      this.ref.close(permissao)
    }
  }
}
