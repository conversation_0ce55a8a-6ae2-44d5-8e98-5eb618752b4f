import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelect } from '@design-tools/interfaces/nebular-select'
import { NbDialogService } from '@nebular/theme'
import moment from 'moment-es6'
import { first } from 'rxjs/operators'

import { DialogAlterPasswordComponent } from './dialog-alter-password/dialog-alter-password.component'
import { ProfileService } from './profile.service'

@Component({
  selector: 'eqp-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
})
export class ProfileComponent implements OnInit {
  public pageTitle: string = 'Usuário | Perfil'
  public loading: boolean = false
  public model: FormGroup

  public statuses: NebularSelect[] = [
    {
      texto: 'Ativo',
      valor: 'ACTIVE',
    },
    {
      texto: 'Inativo',
      valor: 'INACTIVE',
    },
    {
      texto: 'Bloqueado',
      valor: 'BLOCKED',
    },
  ]

  constructor(
    private builder: FormBuilder,
    private profileService: ProfileService,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
  ) {}

  public ngOnInit(): void {
    this.model = this.getNewModel()
    this.find()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      dados: this.builder.group({
        uuid: [],
        nome: [],
        cpf: [],
        email: [],
        status: [],
        criadoEm: [],
        atualizadoEm: [],
        senha: [],
        limitacaoAcesso: ['N'],
        senhaExpira: ['N'],
        dataExpiracaoSenha: [],
        dataUltimaAlteracaoSenha: [],
      }),
    })
  }

  private find(): void {
    this.profileService
      .get()
      .pipe(first())
      .subscribe(({ dados }) => {
        dados.criadoEm = moment(dados.criadoEm).format('DD/MM/YYYY HH:mm:ss')
        dados.atualizadoEm = moment(dados.atualizadoEm).format(
          'DD/MM/YYYY HH:mm:ss',
        )

        if (dados.dataExpiracaoSenha)
          dados.dataExpiracaoSenha = moment(dados.dataExpiracaoSenha).format(
            'DD/MM/YYYY HH:mm:ss',
          )

        this.model.get('dados').patchValue(dados)
      })
  }

  public alterPassword(): void {
    const dialogRef = this.dialogService.open(DialogAlterPasswordComponent, {
      context: {
        dados: {
          ...this.model.get('dados').value,
        },
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        this.toastr.send({
          success: true,
          message: 'A senha foi alterada com sucesso.',
        })
        this.find()
      }
    })
  }
}
