import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { NewsletterComponent } from './newsletter.component'
import { NewsletterFormComponent } from './newsletter-form/newsletter-form.component'
import { NewsletterListComponent } from './newsletter-list/newsletter-list.component'

const routes: Routes = [
  {
    path: '',
    component: NewsletterComponent,
    children: [
      {
        path: '',
        component: NewsletterListComponent,
      },
      {
        path: 'novo',
        component: NewsletterFormComponent,
      },
      {
        path: 'edit/:uuid',
        component: NewsletterFormComponent,
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class NewsletterRoutingModule {}
