import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Observable } from 'rxjs'

import { EQP_APP_CONFIGURATION as eqpConfig } from '../../eqp-configuration'

@Injectable({
  providedIn: 'root',
})
export class MenuService {
  constructor(private http: HttpClient) {}

  public get(): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.get<any>(`menu/${eqpConfig.app.idSistema}/system`, {
      headers,
    })
  }

  public getValidacao(rota: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(
      `menu/${eqpConfig.app.idSistema}/system/rota-validacao?rota=${rota}`,
      {
        headers,
      },
    )
  }

  public getTelaInicial(): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.get<any>(
      `menu/tela-inicial/${eqpConfig.app.idSistema}/system`,
      {
        headers,
      },
    )
  }

  public getEntidades(): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>('auth/entidade', {
      headers,
    })
  }

  public getExercicios(): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>('auth/exercicio', {
      headers,
    })
  }
  getE
}
