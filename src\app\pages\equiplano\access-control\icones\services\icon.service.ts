import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { Observable } from 'rxjs'
import { IconInterface, IconReturnInterface } from './icon'

@Injectable({
  providedIn: 'root',
})
export class IconeService extends CrudService {
  
  constructor(private http: HttpClient) {
    super(http)
  }

  public get(): Observable<IconReturnInterface> {
    const headers = new HttpHeaders();
    
    return this.http.get<IconReturnInterface>('icone', {
      headers,
    });
  }

  public getIndividual(uuid: string): Observable<any> {
    const headers = new HttpHeaders();
    const params = new HttpParams({ fromObject: { uuid } });

    return this.http.get<any>(`icone`, {
      headers,
      params,
    })
  }

  public put(dto: IconInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<IconInterface>(
      `icone/${dto.uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(dto: IconInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<IconInterface>('icone', dto, {
      headers,
      observe: 'response',
    })
  }

  public delete(id: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`icone/${id}`, {
      headers,
    })
  }
  
}
