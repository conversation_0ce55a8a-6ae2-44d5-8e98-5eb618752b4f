import { Component, Input, OnInit } from '@angular/core';
import DataSource from 'devextreme/data/data_source';

@Component({
  selector: 'eqp-operations-historic-grid',
  templateUrl: './operations-historic-grid.component.html',
  styleUrls: ['./operations-historic-grid.component.scss']
})
export class OperationsHistoricGridComponent implements OnInit {
  @Input() historicData: any[] = []
  gridData: DataSource
  constructor() { }

  ngOnInit(): void {
    this.gridData = new DataSource({
      store: {
        data: this.historicData || [],
        key: 'uuid',
        type: 'array'
      },
      sort: [{selector: 'data', desc: true}],
      pageSize: 10,
      paginate: true
    })
  }

}
