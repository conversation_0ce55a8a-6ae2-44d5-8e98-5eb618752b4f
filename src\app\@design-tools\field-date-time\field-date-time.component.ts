import { Component, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import moment from 'moment';

@Component({
  selector: 'eqp-field-datetime',
  templateUrl: './field-date-time.component.html',
  styleUrls: ['./field-date-time.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: FieldDatetimeComponent,
    },
  ],
})
export class FieldDatetimeComponent implements ControlValueAccessor {
  @Input() disabled = false;
  @Input() readonly = false;
  @Input() required = false;
  @Input() label = '';
  @Input() name = '';
  @Input() class = '';
  @Input() placeholder = '00/00/0000 00:00';

  currentValue: Date | string;

  constructor() {}

  onChanged(value: any) {}

  onTouched: (value: any) => {};

  changeValue(value: any) {
    this.currentValue = value;
    let newValue = value ? moment(value).utcOffset(0, true).format() : null;
    this.onChanged(newValue);
    this.onTouched(newValue);
  }

  writeValue(value: any) {
    this.currentValue = value ? new Date(value) : null;
  }

  registerOnChange(fn: any): void {
    this.onChanged = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean) {
    this.disabled = isDisabled;
  }
}
