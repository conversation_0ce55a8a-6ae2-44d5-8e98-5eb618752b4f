import { AccessGroupInterface } from '@pages/administration/access-group/access-group'

export interface UserInterface {
  uuid?: number | string
  nome?: string
  email?: string
  cpf?: string
  senha?: string
  novaSenha?: string
  status?: string | 'ACTIVE' | 'INACTIVE' | 'BLOCKED'
  grupoAcessoUuid?: number | string
  grupoAcesso?: AccessGroupInterface
  criadoEm?: string
  atualizadoEm?: string
}

export interface UserReturnInterface {
  dados: UserInterface[]
  successo: boolean
}
