import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import {
  EmailConfigInterface,
  EmailConfigReturnInterface,
} from '@pages/administration/email-config/email-config'
import { ClientReturnInterface } from '@pages/equiplano/clients/clients'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class EmailConfigService extends CrudService {
  constructor(private http: HttpClient) {
    super(http)
  }

  public get(): Observable<EmailConfigReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<EmailConfigReturnInterface>(`configuracao_email`, {
      headers,
    })
  }

  public getClientes(): Observable<ClientReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ClientReturnInterface>('configuracao_email/cliente', {
      headers,
    })
  }

  public getIndividual(id: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`configuracao_email/${id}`, {
      headers,
    })
  }

  public put(dto: EmailConfigInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<EmailConfigInterface>(
      `configuracao_email/${dto.uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(dto: EmailConfigInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<EmailConfigInterface>(`configuracao_email`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: EmailConfigInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<EmailConfigInterface[]>(
      `configuracao_email/lote`,
      batch,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public delete(id: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`configuracao_email/${id}`, {
      headers,
    })
  }
}
