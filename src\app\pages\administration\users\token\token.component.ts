import { Component, Input, OnInit } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'

import { first } from 'rxjs/operators'
import { LoginService } from '../../../../auth/services/login.service'
import { UsersService } from '../users.service'

@Component({
  selector: 'eqp-token',
  templateUrl: './token.component.html',
  styleUrls: ['./token.component.scss'],
})
export class TokenComponent implements OnInit {
  @Input()
  public user: any

  @Input()
  public nivelPermissao: string

  public gridData: any

  public constructor(
    private service: UsersService,
    private toastr: ToastrService,
    private loginService: LoginService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid()
  }

  private fetchGrid(): void {
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      'auth/sessao',
      10,
      'usuarioUuid',
      this.user.uuid,
    )
  }

  public removerSassao(data: any): void {
    console.log(data)

    this.loginService
      .removerSessao(data.data.uuid)
      .pipe(first())
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Token finalizado',
        })
        this.fetchGrid()
      })
  }

  public finalizado(data: any): boolean {
    return !data.data.tokenFinalizadoEm
  }
}
