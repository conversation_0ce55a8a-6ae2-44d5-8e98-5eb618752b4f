<nb-card>
  <nb-card-header>Anexo</nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="input-group">
      <div class="col-md-1 p-0 mb-2">
        <eqp-nebular-button
          [buttonVisible]="true"
          [buttonType]="'primary'"
          [buttonTitle]="'Selecionar arquivo'"
          [buttonText]="'Arquivo'"
          [buttonIcon]="'fas fa-plus-circle'"
          [buttonIconVisible]="true"
          (buttonEmitter)="file.click()"
          buttonId="upload-file"
        >
        </eqp-nebular-button>

        <span class="opacity-0 visibility-hidden no-outline square-1px">
          <input
            #file
            (change)="uploadFile($event)"
            name="file"
            type="file"
            style="display: none"
          />
        </span>
      </div>

      <div class="col-md-12 px-0">
        <eqp-nebular-input
          [type]="'text'"
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          label="Nome do anexo"
          formControlName="descricao"
          placeholder="Nome do anexo"
          errorMessage="É obrigatório inserir um nome para o anexo"
          required="true"
          maxlength="80"
          id="description-anexo-lib"
          [disabled]="true"
        >
        </eqp-nebular-input>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <button type="button" class="btn btn-dark" (click)="dismiss()">
      Cancelar
    </button>
    <button
      type="button"
      class="btn btn-success float-md-right"
      (click)="adicionar()"
    >
      Adicionar
    </button>
  </nb-card-footer>
</nb-card>
