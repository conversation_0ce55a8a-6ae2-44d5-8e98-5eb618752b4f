import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged } from 'rxjs/operators';

export class StateSubject<T> extends BehaviorSubject<T> {
  private _initialValue!: T;

  constructor(value: T) {
    super(value);
    this._initialValue = value;
  }

  get value$(): Observable<T> {
    return super
      .asObservable()
      .pipe(distinctUntilChanged((a, b) => Object.is(a, b)));
  }

  public update(value: T): void {
    this.next(value);
  }

  public reset(): void {
    this.next(this._initialValue);
  }
}
