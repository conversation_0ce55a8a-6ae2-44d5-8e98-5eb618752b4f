{"items": [{"key": "$base-accent", "value": "rgba(161, 110, 255, 1)"}, {"key": "$base-bg", "value": "rgba(50, 50, 89, 1)"}, {"key": "$base-border-color", "value": "rgba(27, 27, 56, 1)"}, {"key": "$base-success", "value": "rgba(0, 214, 143, 1)"}, {"key": "$base-warning", "value": "rgba(255, 170, 0, 1)"}, {"key": "$base-danger", "value": "rgba(255, 61, 113, 1)"}, {"key": "$button-default-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$button-success-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$button-danger-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$toast-info-bg", "value": "rgba(161, 110, 255, 1)"}, {"key": "$base-hover-bg", "value": "rgba(255, 255, 255, 0)"}, {"key": "$base-focus-bg", "value": "rgba(255, 255, 255, 0)"}, {"key": "$badge-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$pager-page-selected-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$datagrid-row-focused-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$datagrid-selection-bg", "value": "rgba(161, 110, 255, 0.25)"}, {"key": "$datagrid-row-error-bg", "value": "rgba(255, 61, 113, 0.7)"}, {"key": "$scheduler-appointment-text-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$texteditor-border-color", "value": "rgba(27, 27, 56, 0.7)"}, {"key": "$texteditor-hover-border-color", "value": "rgba(161, 110, 255, 0.5)"}, {"key": "$tooltip-bg", "value": "rgba(161, 110, 255, 1)"}, {"key": "$material-slider-tooltip-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$accordion-title-color", "value": "rgba(161, 110, 255, 1)"}, {"key": "$navbar-tab-selected-color", "value": "rgba(161, 110, 255, 1)"}, {"key": "$treeview-focused-bg", "value": "rgba(161, 110, 255, 0.26)"}, {"key": "$drawer-shader-background-color", "value": "rgba(161, 110, 255, 0.5)"}, {"key": "$scrollable-scroll-bg", "value": "rgba(161, 110, 255, 0.7)"}, {"key": "$fieldset-field-label-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$texteditor-bg", "value": "rgba(255, 255, 255, 0)"}, {"key": "$texteditor-hover-bg", "value": "rgba(255, 255, 255, 0)"}, {"key": "$datagrid-menu-icon-color", "value": "rgba(161, 110, 255, 1)"}], "baseTheme": "material.purple.dark.compact", "outputColorScheme": "nebular-purple", "makeSwatch": false, "version": "21.2.6", "widgets": [], "removeExternalResources": false}