# sass-lint config generated by make-sass-lint-config v0.1.2
#
# The following scss-lint Linters are not yet supported by sass-lint:
# DisableLinterReason, ElsePlacement, PropertyCount, SelectorDepth
# SpaceAroundOperator, TrailingWhitespace, UnnecessaryParentReference, Compass::*
#
# The following settings/values are unsupported by sass-lint:
# Linter Indentation, option "allow_non_nested_indentation"
# Linter Indentation, option "character"
# Linter NestingDepth, option "ignore_parent_selectors"
# Linter PropertySortOrder, option "min_properties"
# Linter PropertySortOrder, option "separate_groups"
# Linter SpaceBeforeBrace, option "allow_single_line_padding"
# Linter VendorPrefix, option "identifier_list"

files:
  include: 'src/**/*.scss'
options:
  formatter: stylish
  merge-default-rules: false
rules:
  bem-depth:
    - 0
    - max-depth: 1
  border-zero:
    - 1
    - convention: zero
  brace-style:
    - 1
    - allow-single-line: true
  class-name-format:
    - 2
    - convention: hyphenatedbem
  clean-import-paths:
    - 1
    - filename-extension: false
      leading-underscore: false
  empty-line-between-blocks:
    - 1
    - ignore-single-line-rulesets: true
  extends-before-declarations: 1
  extends-before-mixins: 1
  final-newline:
    - 1
    - include: true
  force-attribute-nesting: 1
  force-element-nesting: 1
  force-pseudo-nesting: 1
  function-name-format:
    - 1
    - allow-leading-underscore: true
      convention: hyphenatedlowercase
  hex-length:
    - 1
    - style: short
  hex-notation:
    - 1
    - style: lowercase
  id-name-format:
    - 1
    - convention: hyphenatedlowercase
  indentation: 0
  leading-zero:
    - 1
    - include: false
  mixin-name-format:
    - 1
    - allow-leading-underscore: true
      convention: hyphenatedlowercase
  mixins-before-declarations: 1
  nesting-depth:
    - 1
    - max-depth: 3
  no-color-keywords: 1
  no-color-literals: 1
  no-css-comments: 1
  no-debug: 1
  no-duplicate-properties: 1
  no-empty-rulesets: 1
  no-extends: 0
  no-ids: 1
  no-important: 0
  no-invalid-hex: 1
  no-mergeable-selectors: 1
  no-misspelled-properties:
    - 1
    - extra-properties: []
  no-qualifying-elements:
    - 1
    - allow-element-with-attribute: false
      allow-element-with-class: false
      allow-element-with-id: false
  no-trailing-zero: 1
  no-transition-all: 0
  no-url-protocols: 0
  no-vendor-prefixes:
    - 1
    - additional-identifiers: []
      excluded-identifiers: []
  placeholder-in-extend: 1
  placeholder-name-format:
    - 1
    - convention: hyphenatedlowercase
  property-sort-order:
    - 1
    - ignore-custom-properties: false
  property-units:
    - 1
    - global:
        - ch
        - em
        - ex
        - rem
        - cm
        - in
        - mm
        - pc
        - pt
        - px
        - q
        - vh
        - vw
        - vmin
        - vmax
        - deg
        - grad
        - rad
        - turn
        - ms
        - s
        - Hz
        - kHz
        - dpi
        - dpcm
        - dppx
        - '%'
      per-property: {}
  pseudo-element: true
  quotes:
    - 1
    - style: single
  shorthand-values:
    - 1
    - allowed-shorthands:
        - 1
        - 2
        - 3
  single-line-per-selector: 1
  space-after-bang:
    - 1
    - include: false
  space-after-colon:
    - 1
    - include: true
  space-after-comma:
    - 1
    - include: true
  space-before-bang:
    - 1
    - include: true
  space-before-brace:
    - 1
    - include: true
  space-before-colon: 1
  space-between-parens:
    - 1
    - include: false
  trailing-semicolon: 1
  url-quotes: 1
  variable-for-property:
    - 0
    - properties: []
  variable-name-format:
    - 1
    - allow-leading-underscore: true
      convention: hyphenatedlowercase
  zero-unit: 1
