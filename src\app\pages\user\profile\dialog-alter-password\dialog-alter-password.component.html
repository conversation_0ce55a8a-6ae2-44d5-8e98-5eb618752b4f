<eqp-standard-page
  [mainTitle]="'Altera Senha'"
  [spinnerActive]="false"
  [spinnerStatus]="'info'"
  [rightApproveButtonVisible]="true"
  [rightApproveButtonId]="'CONFIRMAR'"
  (rightApproveButtonEmitter)="alterPassword()"
  [bottomLeftButtonVisible]="true"
  [bottomLeftButtonId]="'VOLTAR'"
  [bottomLeftButtonText]="'VOLTAR'"
  (bottomLeftButtonEmitter)="closeAlterPassword()"
>
  <ng-container>
    <form [formGroup]="model">
      <div class="row">
        <div class="col-12">
          <eqp-nebular-input
            [style]="'basic'"
            [type]="'password'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="senha"
            name="senha"
            label="Senha"
            placeholder="Senha"
            required
            errorMessage="É obrigatório preencher a senha"
            [maxlength]="100"
          >
          </eqp-nebular-input>
        </div>

        <div class="col-12">
          <eqp-nebular-input
            [style]="'basic'"
            [type]="'password'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="novaSenha"
            name="Nova Senha"
            label="Nova Senha"
            placeholder="Nova Senha"
            required
            errorMessage="É obrigatório preencher a nova senha"
            [maxlength]="100"
          >
          </eqp-nebular-input>
        </div>

        <div class="col-12">
          <eqp-nebular-input
            [style]="'basic'"
            [type]="'password'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="novaSenhaConfirmacao"
            name="Confirmar Nova Senha"
            label="Confirmar Nova Senha"
            placeholder="Confirmar Nova Senha"
            required
            errorMessage="É obrigatório preencher o confirmar nova senha"
            [maxlength]="100"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </form>
  </ng-container>
</eqp-standard-page>
