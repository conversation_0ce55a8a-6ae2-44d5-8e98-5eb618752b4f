import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ApiResponse } from '@core/data/api-response'
import { ResponseDto } from '@core/data/response-dto'
import { Observable } from 'rxjs'
import { NewsletterInterface } from './newsletter'
import { SistemaModuloNome } from './modules-enum'

interface BoletimResponse {
  data: NewsletterInterface[]
  totalCount: number
}

@Injectable({
  providedIn: 'root',
})
export class NewsletterService {
  private readonly MODULE_KEY = 'BACK_OFFICE'

  constructor(private _http: HttpClient) {}

  public loadModules() {
    const headers = new HttpHeaders()
    return this._http.get<ApiResponse<any>>(
      'boletim-informativo/modulos?take=0',
      {
        headers,
      },
    )
  }

  // mostra a quantidade de boletins não lidos para o módulo logado
  public loadTotalNewsletters() {
    const headers = new HttpHeaders()
    return this._http.get<ResponseDto<number>>(
      `boletim-informativo/notificacao/${this.MODULE_KEY}`,
      {
        headers,
      },
    )
  }

  public getAll() {
    const headers = new HttpHeaders()
    return this._http.get<ApiResponse<NewsletterInterface>>(
      'boletim-informativo?take=0',
      {
        headers,
      },
    )
  }

  public getAllByModule(dataParams: {
    moduloUuid: string
    skip: number
    take: number,
    termoPesquisa?: string
  }) {
    const { moduloUuid, skip, take, termoPesquisa } = dataParams
    const headers = new HttpHeaders()
    let filter = [
      ['moduloUuid', '=', moduloUuid],
      'and',
      ['publicar', '=', 'S'],
    ]
    if(termoPesquisa && termoPesquisa.trim() !== '') {
      console.log(termoPesquisa)
      filter = filter.concat(["and", ['titulo', 'contains', termoPesquisa]])
    }

    console.log(filter)
    let params = new HttpParams().append('filter', JSON.stringify(filter))
    params = params.append('skip', skip.toString())
    params = params.append('take', take.toString())

    return this._http.get<BoletimResponse>('boletim-informativo', {
      headers,
      params,
    })
  }

  public getByUiid(uuid: string) {
    const headers = new HttpHeaders()

    return this._http.get<ResponseDto<NewsletterInterface>>(
      `boletim-informativo/${uuid}`,
      {
        headers,
      },
    )
  }

  public put(dto: NewsletterInterface) {
    const headers = new HttpHeaders()

    return this._http.put<ApiResponse<NewsletterInterface>>(
      `boletim-informativo/${dto.uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(dto: NewsletterInterface) {
    const headers = new HttpHeaders()

    return this._http.post<ApiResponse<NewsletterInterface>>(
      'boletim-informativo',
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public delete(uuid: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this._http.delete<NewsletterInterface>(
      `boletim-informativo/${uuid}`,
      {
        headers,
      },
    )
  }

  public publicar(uuid: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this._http.put<NewsletterInterface>(
      `boletim-informativo/publicar/${uuid}`,
      {
        headers,
      },
    )
  }

  public listaPublicacao() {
    const headers = new HttpHeaders()

    return this._http.get<ApiResponse<any>>(
      'boletim-informativo/lista-usuario',
      {
        headers,
      },
    )
  }

  obterNomeModulo(): string {
    return SistemaModuloNome[this.MODULE_KEY]
  }
}
