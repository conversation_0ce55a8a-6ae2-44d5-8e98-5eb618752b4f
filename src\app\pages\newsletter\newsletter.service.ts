import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ApiResponse } from '@core/data/api-response'
import { ResponseDto } from '@core/data/response-dto'
import { Observable } from 'rxjs'
import { NewsletterInterface } from './newsletter'
import { SistemaModuloNome } from './modules-enum'

interface BoletimResponse {
  data: NewsletterInterface[]
  totalCount: number
}

@Injectable({
  providedIn: 'root',
})
export class NewsletterService {
  constructor(private _http: HttpClient) {}

  public getByUiid(uuid: string) {
    const headers = new HttpHeaders()

    return this._http.get<ResponseDto<NewsletterInterface>>(
      `boletim-informativo/${uuid}`,
      {
        headers,
      },
    )
  }

  public put(dto: NewsletterInterface) {
    const headers = new HttpHeaders()

    return this._http.put<ApiResponse<NewsletterInterface>>(
      `boletim-informativo/${dto.uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(dto: NewsletterInterface) {
    const headers = new HttpHeaders()

    return this._http.post<ApiResponse<NewsletterInterface>>(
      'boletim-informativo',
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public delete(uuid: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this._http.delete<NewsletterInterface>(
      `boletim-informativo/${uuid}`,
      {
        headers,
      },
    )
  }

  public publicar(uuid: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this._http.put<NewsletterInterface>(
      `boletim-informativo/publicar/${uuid}`,
      {
        headers,
      },
    )
  }
}
