import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { DxColumnInterface } from '@design-tools/interfaces/column-interface'
import { NebularSelect } from '@design-tools/interfaces/nebular-select'
import {
  ActionsReturnInterface,
} from '@pages/equiplano/access-control/actions/actions'
import {
  ActionsService,
} from '@pages/equiplano/access-control/actions/actions.service'
import {
  MicroServicesInterface,
  MicroServicesReturnInterface,
} from '@pages/equiplano/access-control/micro-services/micro-service'
import {
  MicroServicesService,
} from '@pages/equiplano/access-control/micro-services/micro-services.service'
import { ClientReturnInterface } from '@pages/equiplano/clients/clients'
import { ClientsService } from '@pages/equiplano/clients/clients.service'
import { DxDataGridComponent } from 'devextreme-angular'
import { Subscription } from 'rxjs'
import { finalize } from 'rxjs/operators'

@Component({
  selector: 'eqp-micro-services',
  templateUrl: './micro-services.component.html',
  styleUrls: ['./micro-services.component.scss'],
})
export class MicroServicesComponent implements OnInit, OnDestroy {
  public loading: boolean = false
  public gridData: MicroServicesInterface[] = []
  public columnsTemplate: DxColumnInterface[] = []
  public actions: NebularSelect[] = []
  public clients: NebularSelect[] = []

  private subscription: Subscription
  private actionsSubscription: Subscription
  private clientsSubscription: Subscription

  @ViewChild('dataGrid', { static: false }) dataGrid: DxDataGridComponent
  public bloqueia: boolean = false

  constructor(
    private service: MicroServicesService,
    private actionsService: ActionsService,
    private clientsService: ClientsService,
    private toastr: ToastrService,
  ) {}

  public ngOnInit(): void {
    this.columnsTemplate = this.getColumnsTemplate()
    this.getActions()
    this.getClients()
    this.fetchGrid()
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
    if (this.actionsSubscription) this.actionsSubscription.unsubscribe()
    if (this.clientsSubscription) this.clientsSubscription.unsubscribe()
  }

  public fetchGrid(): void {
    this.loading = true
    this.subscription = this.service
      .get()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: MicroServicesReturnInterface) => {
          this.gridData = data.dados
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getActions(): void {
    this.loading = true
    this.actionsSubscription = this.actionsService
      .get()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: ActionsReturnInterface) => {
          data.dados.forEach(action => {
            this.actions.push({
              texto: action.nome,
              valor: action.uuid,
            })
          })
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getClients(): void {
    this.loading = true
    this.clientsSubscription = this.clientsService
      .get()
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        (data: ClientReturnInterface) => {
          data.dados.forEach(client => {
            this.clients.push({
              texto: client.nome,
              valor: client.uuid,
            })
          })
        },
        (err: any) => {
          if (err.mensagens) this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  private getColumnsTemplate(): DxColumnInterface[] {
    const template: DxColumnInterface[] = [
      {
        caption: 'Código',
        dataField: 'uuid',
        visible: false,
        allowEditing: false,
        allowHiding: false,
        formItem: {
          visible: false,
        },
      },
      {
        caption: 'Nome',
        dataField: 'nome',
        formItem: {
          isRequired: true,
          editorOptions: { maxLength: '100', inputAttr: { id: 'Nome' } },
        },
      },
      {
        caption: 'Módulo',
        dataField: 'acaoUuid',
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Módulo' } },
        },
        lookup: {
          dataSource: this.actions,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Cliente',
        dataField: 'clienteUuid',
        formItem: {
          isRequired: true,
          editorOptions: { inputAttr: { id: 'Cliente' } },
        },
        lookup: {
          dataSource: this.clients,
          displayExpr: 'texto',
          valueExpr: 'valor',
        },
      },
      {
        caption: 'Ações',
        type: 'buttons',
        buttons: [
          {
            name: 'edit',
            icon: 'fas fa-edit',
          },
          {
            name: 'delete',
            icon: 'fas fa-trash-alt',
          },
        ],
      },
    ]
    return template
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items[1].showText = 'ever'
    event.toolbarOptions.items[1].options.text = 'Novo serviço'
    event.toolbarOptions.items[1].options.hint = 'Novo micro serviço'

    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public onRowinserting(event: any): void {
    this.loading = true
    const { nome, clienteUuid, acaoUuid } = event.data
    event.cancel = true

    const dto = {
      nome,
      clienteUuid,
      acaoUuid,
    }

    this.subscription = this.service
      .post(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Micro serviço inserido com sucesso.',
          })
          this.fetchGrid()
          this.dataGrid.instance.cancelEditData()
        },
        (err: any) => {
          this.fetchGrid()
          this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  public onRowUpdating(event: any): void {
    this.loading = true
    const { nome, clienteUuid, acaoUuid } = event.newData
    event.cancel = true

    const dto = {
      uuid: event.oldData.uuid,
      nome: nome ? nome : event.oldData.nome,
      clienteUuid: clienteUuid ? clienteUuid : event.oldData.clienteUuid,
      acaoUuid: acaoUuid ? acaoUuid : event.oldData.acaoUuid,
    }

    this.subscription = this.service
      .put(dto)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message:
              'O micro serviço ' + event.oldData.nome + ' foi atualizado.',
          })
          this.fetchGrid()
          this.dataGrid.instance.cancelEditData()
        },
        (err: any) => {
          this.fetchGrid()
          this.toastr.bulkSend(err.mensagens)
        },
      )
  }

  public onRowRemoving(event: any): void {
    this.subscription = this.service.delete(event.data.uuid).subscribe(
      () => {
        this.toastr.send({
          success: true,
          message:
            'Micro serviço ' + event.data.nome + ' excluído com sucesso.',
        })
      },
      (resp: any) => this.toastr.bulkSend(resp.mensagens),
    )
  }

  public onRowRemoved(event: any): void {
    setTimeout(() => {
      this.fetchGrid()
    }, 200)
  }

  public bloquearFuncoes(block): void {
    this.bloqueia = block
  }
}
