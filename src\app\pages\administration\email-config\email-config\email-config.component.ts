import { AfterViewInit, Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'

import { EmailConfigService } from './../email-config.service'

@Component({
  selector: 'eqp-email-config',
  templateUrl: './email-config.component.html',
  styleUrls: ['./email-config.component.scss'],
})
export class EmailConfigComponent
  extends BaseTelasComponent
  implements OnInit, AfterViewInit
{
  public loading: boolean = false
  public pageTitle: string = 'Administração | Configuração de envio de e-mail'
  public formulario: FormGroup

  public clientesData: any

  constructor(
    private service: EmailConfigService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
    this.permissao('/administracao/configuracao-email')
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.setFocus(), 1000
    })
  }

  private setFocus(): void {
    document.getElementById('Servidor de saída SMTP').focus()
  }

  private loadSelects(): void {
    this.clientesData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'configuracao_email/cliente',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      emailConfig: this.formBuilder.group({
        uuid: [''],
        smtpEndereco: ['', [Validators.required, Validators.maxLength(100)]],
        smtpPorta: ['', [Validators.required, Validators.maxLength(100)]],
        smtpUsuario: [
          '',
          [Validators.required, Validators.maxLength(100), Validators.email],
        ],
        smtpSenha: ['', [Validators.required, Validators.maxLength(100)]],
        clienteUuid: ['', Validators.required],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) this.buscar(uuid)
      this.loadSelects()
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('emailConfig').patchValue(data.dados)
        this.loadSelects()
      })
  }

  public cancelar(): void {
    this.gravarParametros()
    this.router.navigate([`administracao/configuracao-email`])
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.loading = true
        this.service
          .delete(this.formulario.get('emailConfig.uuid').value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Configuração de e-mail excluída com sucesso.',
              })
              this.cancelar()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Falta preencher os campos obrigatórios',
      })
      this.formulario.markAllAsTouched()
    } else {
      this.loading = true
      if (this.formulario.get('emailConfig.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getEmailConfDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Configuração de e-mail criada com sucesso.',
        })
        this.cancelar()
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getEmailConfDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Configuração de e-mail atualizado com sucesso.',
        })
        this.cancelar()
      })
  }

  private getEmailConfDto(): any {
    const dto = this.formulario.getRawValue()

    return dto.emailConfig
  }
}
