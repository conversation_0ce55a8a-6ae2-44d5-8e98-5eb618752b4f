import { Component, Input, OnInit } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import DataSource from 'devextreme/data/data_source'
import { first } from 'rxjs/operators'

import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogService } from '@nebular/theme'
import { GrupoAcessoListaComponent } from '../grupo-acesso-lista/grupo-acesso-lista.component'
import { UsersService } from './../users.service'

@Component({
  selector: 'eqp-access-group',
  templateUrl: './access-group.component.html',
  styleUrls: ['./access-group.component.scss'],
})
export class AccessGroupComponent implements OnInit {
  @Input()
  public user: any

  @Input()
  public nivelPermissao: string

  public gridData: any
  public gruposAcesso: any
  public loading: boolean = false

  public constructor(
    private service: UsersService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid()
  }

  private fetchGrid(): void {
    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        `usuario/grupo-acesso/listagem-grid/${this.user.uuid}`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.name === 'addRowButton') {
        item.showText = 'ever'
        item.options.text = 'Novo grupo'
        item.options.hint = 'Novo grupo de acesso'
        item.options.onClick = () => this.novoRegistro()
      }

      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    const dialogRef = this.dialogService.open(GrupoAcessoListaComponent, {
      context: {
        user: this.user,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })

    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno.length > 0) {
        this.service
          .postGrupoAcesso(this.user.uuid, retorno)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Grupo de acesso do usuário incluido com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .deleteGrupoAcesso(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Grupo de acesso do usuário excluído com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
