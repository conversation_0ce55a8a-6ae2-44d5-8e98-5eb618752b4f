import { Component, OnInit, ViewChild } from '@angular/core'

import { ActionsComponent } from '../actions/actions.component'

@Component({
  selector: 'eqp-actions-list',
  templateUrl: './actions-list.component.html',
  styleUrls: ['./actions-list.component.scss'],
})
export class ActionsListComponent implements OnInit {
  public pageTitle: string = 'Controle de acesso | Menus'

  @ViewChild('actions', { static: true }) actions: ActionsComponent

  constructor() {}

  public ngOnInit(): void {}

  public fetchGrid(): void {}
}
