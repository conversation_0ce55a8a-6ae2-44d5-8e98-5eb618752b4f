<eqp-standard-page [mainTitle]="pageTitle" [formGroup]="model" [rightApproveButtonDisabled]="model.invalid">
  <eqp-loading *ngIf="loading"></eqp-loading>
  <div class="container">
    <div class="row">
      <div class="col col-12 col-md-3">
        <eqp-field-date label="Período Inicial *" formControlName="periodoInicial"></eqp-field-date>
      </div>
      <div class="col col-12 col-md-3">
        <eqp-field-date label="Período Final *" formControlName="periodoFinal"></eqp-field-date>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col col-12">
        <eqp-search-field waitingTime="2000" mensageNotFound="Usuário não encontrado" label="Usuário" uri="usuario"
          codeKey="cpf" codeLabel="Cpf" nameKey="nome" nameLabel="Nome" searchColumnsType="userColumns"
          dialogTitle="Usuário" formControlName="usuarioUuid"></eqp-search-field>
      </div>
    </div>
    <div class="d-flex justify-content-end mt-3 mb-3" style="gap: 0.5rem">
      <button type="button" class="btn btn-dark" (click)="eraseSearch()">Limpar pesquisa</button>
      <button type="button" [disabled]="model.invalid" class="btn btn-success"
        (click)="getGridData()">Pesquisar</button>
    </div>
  </div>

  <ng-container>
    <dx-data-grid id="usuarioGrid" [dataSource]="gridData" [allowColumnResizing]="true" [columnAutoWidth]="true"
      [showColumnLines]="false" [showRowLines]="false" [showBorders]="false" [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true" [loadPanel]="false" [columnHidingEnabled]="true" [remoteOperations]="true"
      keyExpr="uuid">
      <dxo-state-storing [enabled]="true" type="custom" savingTimeout="100"></dxo-state-storing>
      <dxo-export [enabled]="true" [excelWrapTextEnabled]="true" [excelFilterEnabled]="true"
        [fileName]="pageTitle"></dxo-export>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager [showInfo]="true" [showNavigationButtons]="true" [showPageSizeSelector]="false">
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

      <dxo-search-panel [visible]="true" placeholder="Buscar usuário"></dxo-search-panel>

      <dxo-editing mode="form" [allowUpdating]="false" [allowDeleting]="false" [allowAdding]="false" [useIcons]="true">
      </dxo-editing>

      <dxi-column dataField="nome" caption="Nome"></dxi-column>

      <dxi-column dataField="cpf" caption="CPF" cellTemplate="cpfcnpjTemplate"></dxi-column>

      <div *dxTemplate="let data of 'cpfcnpjTemplate'">
        {{ data.value | cpfCnpj }}
      </div>

      <dxo-master-detail [enabled]="true" template="details"></dxo-master-detail>
      <div *dxTemplate="let detail of 'details'">
        <eqp-operations-detail [logAccessData]="detail.data.informacoes"></eqp-operations-detail>
      </div>
    </dx-data-grid>
  </ng-container>
</eqp-standard-page>
