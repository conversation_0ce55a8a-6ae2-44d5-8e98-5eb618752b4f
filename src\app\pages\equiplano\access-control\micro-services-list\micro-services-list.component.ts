import { Component, OnInit, ViewChild } from '@angular/core'

import {
  MicroServicesComponent,
} from './../micro-services/micro-services.component'

@Component({
  selector: 'eqp-micro-services-list',
  templateUrl: './micro-services-list.component.html',
  styleUrls: ['./micro-services-list.component.scss'],
})
export class MicroServicesListComponent implements OnInit {
  public pageTitle: string = 'Controle de acesso | Micro serviços'

  @ViewChild('micro-services', { static: true })
  microServices: MicroServicesComponent

  constructor() {}

  public ngOnInit(): void {}

  public fetchGrid(): void {
    this.microServices.fetchGrid()
  }
}
