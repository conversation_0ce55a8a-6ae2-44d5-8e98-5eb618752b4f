export interface ParameterInterface {
  uuid?: number | string
  verbo?: string
  campo?: string
  funcao?: string
  obrigatoriedade?: string
  tamanho?: string
  tipo?: string
  valorPadrao?: string
  acaoUuid?: string
  clienteUuid?: string
  microservicoId?: string
  criadoEm?: string
  atualizadoEm?: string
}

export interface ParameterReturnInterface {
  dados: ParameterInterface[]
  successo: boolean
}
