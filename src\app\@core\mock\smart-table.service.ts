import { Injectable } from '@angular/core';
import { SmartTableData } from '../data/smart-table';

@Injectable()
export class SmartTableService extends SmartTableData {

  data = [{
    id: 1,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    username: '@mdo',
    email: '<EMAIL>',
    age: '28',
  }, {
    id: 2,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    username: '@fat',
    email: '<EMAIL>',
    age: '45',
  }, {
    id: 3,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    username: '@twitter',
    email: '<EMAIL>',
    age: '18',
  }, {
    id: 4,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    username: '@snow',
    email: '<EMAIL>',
    age: '20',
  }, {
    id: 5,
    firstName: '<PERSON>',
    lastName: 'Sparrow',
    username: '@jack',
    email: '<EMAIL>',
    age: '30',
  }, {
    id: 6,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    username: '@ann',
    email: '<EMAIL>',
    age: '21',
  }, {
    id: 7,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    username: '@barbara',
    email: '<EMAIL>',
    age: '43',
  }, {
    id: 8,
    firstName: 'Sevan',
    lastName: 'Bagrat',
    username: '@sevan',
    email: '<EMAIL>',
    age: '13',
  }, {
    id: 9,
    firstName: 'Ruben',
    lastName: 'Vardan',
    username: '@ruben',
    email: '<EMAIL>',
    age: '22',
  }, {
    id: 10,
    firstName: 'Karen',
    lastName: 'Sevan',
    username: '@karen',
    email: '<EMAIL>',
    age: '33',
  }, {
    id: 11,
    firstName: 'Mark',
    lastName: 'Otto',
    username: '@mark',
    email: '<EMAIL>',
    age: '38',
  }, {
    id: 12,
    firstName: 'Jacob',
    lastName: 'Thornton',
    username: '@jacob',
    email: '<EMAIL>',
    age: '48',
  }, {
    id: 13,
    firstName: 'Haik',
    lastName: 'Hakob',
    username: '@haik',
    email: '<EMAIL>',
    age: '48',
  }, {
    id: 14,
    firstName: 'Garegin',
    lastName: 'Jirair',
    username: '@garegin',
    email: '<EMAIL>',
    age: '40',
  }, {
    id: 15,
    firstName: 'Krikor',
    lastName: 'Bedros',
    username: '@krikor',
    email: '<EMAIL>',
    age: '32',
  }, {
    'id': 16,
    'firstName': 'Francisca',
    'lastName': 'Brady',
    'username': '@Gibson',
    'email': '<EMAIL>',
    'age': 11,
  }, {
    'id': 17,
    'firstName': 'Tillman',
    'lastName': 'Figueroa',
    'username': '@Snow',
    'email': '<EMAIL>',
    'age': 34,
  }, {
    'id': 18,
    'firstName': 'Jimenez',
    'lastName': 'Morris',
    'username': '@Bryant',
    'email': '<EMAIL>',
    'age': 45,
  }, {
    'id': 19,
    'firstName': 'Sandoval',
    'lastName': 'Jacobson',
    'username': '@Mcbride',
    'email': '<EMAIL>',
    'age': 32,
  }, {
    'id': 20,
    'firstName': 'Griffin',
    'lastName': 'Torres',
    'username': '@Charles',
    'email': '<EMAIL>',
    'age': 19,
  }, {
    'id': 21,
    'firstName': 'Cora',
    'lastName': 'Parker',
    'username': '@Caldwell',
    'email': '<EMAIL>',
    'age': 27,
  }, {
    'id': 22,
    'firstName': 'Cindy',
    'lastName': 'Bond',
    'username': '@Velez',
    'email': '<EMAIL>',
    'age': 24,
  }, {
    'id': 23,
    'firstName': 'Frieda',
    'lastName': 'Tyson',
    'username': '@Craig',
    'email': '<EMAIL>',
    'age': 45,
  }, {
    'id': 24,
    'firstName': 'Cote',
    'lastName': 'Holcomb',
    'username': '@Rowe',
    'email': '<EMAIL>',
    'age': 20,
  }, {
    'id': 25,
    'firstName': 'Trujillo',
    'lastName': 'Mejia',
    'username': '@Valenzuela',
    'email': '<EMAIL>',
    'age': 16,
  }, {
    'id': 26,
    'firstName': 'Pruitt',
    'lastName': 'Shepard',
    'username': '@Sloan',
    'email': '<EMAIL>',
    'age': 44,
  }, {
    'id': 27,
    'firstName': 'Sutton',
    'lastName': 'Ortega',
    'username': '@Black',
    'email': '<EMAIL>',
    'age': 42,
  }, {
    'id': 28,
    'firstName': 'Marion',
    'lastName': 'Heath',
    'username': '@Espinoza',
    'email': '<EMAIL>',
    'age': 47,
  }, {
    'id': 29,
    'firstName': 'Newman',
    'lastName': 'Hicks',
    'username': '@Keith',
    'email': '<EMAIL>',
    'age': 15,
  }, {
    'id': 30,
    'firstName': 'Boyle',
    'lastName': 'Larson',
    'username': '@Summers',
    'email': '<EMAIL>',
    'age': 32,
  }, {
    'id': 31,
    'firstName': 'Haynes',
    'lastName': 'Vinson',
    'username': '@Mckenzie',
    'email': '<EMAIL>',
    'age': 15,
  }, {
    'id': 32,
    'firstName': 'Miller',
    'lastName': 'Acosta',
    'username': '@Young',
    'email': '<EMAIL>',
    'age': 55,
  }, {
    'id': 33,
    'firstName': 'Johnston',
    'lastName': 'Brown',
    'username': '@Knight',
    'email': '<EMAIL>',
    'age': 29,
  }, {
    'id': 34,
    'firstName': 'Lena',
    'lastName': 'Pitts',
    'username': '@Forbes',
    'email': '<EMAIL>',
    'age': 25,
  }, {
    'id': 35,
    'firstName': 'Terrie',
    'lastName': 'Kennedy',
    'username': '@Branch',
    'email': '<EMAIL>',
    'age': 37,
  }, {
    'id': 36,
    'firstName': 'Louise',
    'lastName': 'Aguirre',
    'username': '@Kirby',
    'email': '<EMAIL>',
    'age': 44,
  }, {
    'id': 37,
    'firstName': 'David',
    'lastName': 'Patton',
    'username': '@Sanders',
    'email': '<EMAIL>',
    'age': 26,
  }, {
    'id': 38,
    'firstName': 'Holden',
    'lastName': 'Barlow',
    'username': '@Mckinney',
    'email': '<EMAIL>',
    'age': 11,
  }, {
    'id': 39,
    'firstName': 'Baker',
    'lastName': 'Rivera',
    'username': '@Montoya',
    'email': '<EMAIL>',
    'age': 47,
  }, {
    'id': 40,
    'firstName': 'Belinda',
    'lastName': 'Lloyd',
    'username': '@Calderon',
    'email': '<EMAIL>',
    'age': 21,
  }, {
    'id': 41,
    'firstName': 'Pearson',
    'lastName': 'Patrick',
    'username': '@Clements',
    'email': '<EMAIL>',
    'age': 42,
  }, {
    'id': 42,
    'firstName': 'Alyce',
    'lastName': 'Mckee',
    'username': '@Daugherty',
    'email': '<EMAIL>',
    'age': 55,
  }, {
    'id': 43,
    'firstName': 'Valencia',
    'lastName': 'Spence',
    'username': '@Olsen',
    'email': '<EMAIL>',
    'age': 20,
  }, {
    'id': 44,
    'firstName': 'Leach',
    'lastName': 'Holcomb',
    'username': '@Humphrey',
    'email': '<EMAIL>',
    'age': 28,
  }, {
    'id': 45,
    'firstName': 'Moss',
    'lastName': 'Baxter',
    'username': '@Fitzpatrick',
    'email': '<EMAIL>',
    'age': 51,
  }, {
    'id': 46,
    'firstName': 'Jeanne',
    'lastName': 'Cooke',
    'username': '@Ward',
    'email': '<EMAIL>',
    'age': 59,
  }, {
    'id': 47,
    'firstName': 'Wilma',
    'lastName': 'Briggs',
    'username': '@Kidd',
    'email': '<EMAIL>',
    'age': 53,
  }, {
    'id': 48,
    'firstName': 'Beatrice',
    'lastName': 'Perry',
    'username': '@Gilbert',
    'email': '<EMAIL>',
    'age': 39,
  }, {
    'id': 49,
    'firstName': 'Whitaker',
    'lastName': 'Hyde',
    'username': '@Mcdonald',
    'email': '<EMAIL>',
    'age': 35,
  }, {
    'id': 50,
    'firstName': 'Rebekah',
    'lastName': 'Duran',
    'username': '@Gross',
    'email': '<EMAIL>',
    'age': 40,
  }, {
    'id': 51,
    'firstName': 'Earline',
    'lastName': 'Mayer',
    'username': '@Woodward',
    'email': '<EMAIL>',
    'age': 52,
  }, {
    'id': 52,
    'firstName': 'Moran',
    'lastName': 'Baxter',
    'username': '@Johns',
    'email': '<EMAIL>',
    'age': 20,
  }, {
    'id': 53,
    'firstName': 'Nanette',
    'lastName': 'Hubbard',
    'username': '@Cooke',
    'email': '<EMAIL>',
    'age': 55,
  }, {
    'id': 54,
    'firstName': 'Dalton',
    'lastName': 'Walker',
    'username': '@Hendricks',
    'email': '<EMAIL>',
    'age': 25,
  }, {
    'id': 55,
    'firstName': 'Bennett',
    'lastName': 'Blake',
    'username': '@Pena',
    'email': '<EMAIL>',
    'age': 13,
  }, {
    'id': 56,
    'firstName': 'Kellie',
    'lastName': 'Horton',
    'username': '@Weiss',
    'email': '<EMAIL>',
    'age': 48,
  }, {
    'id': 57,
    'firstName': 'Hobbs',
    'lastName': 'Talley',
    'username': '@Sanford',
    'email': '<EMAIL>',
    'age': 28,
  }, {
    'id': 58,
    'firstName': 'Mcguire',
    'lastName': 'Donaldson',
    'username': '@Roman',
    'email': '<EMAIL>',
    'age': 38,
  }, {
    'id': 59,
    'firstName': 'Rodriquez',
    'lastName': 'Saunders',
    'username': '@Harper',
    'email': '<EMAIL>',
    'age': 20,
  }, {
    'id': 60,
    'firstName': 'Lou',
    'lastName': 'Conner',
    'username': '@Sanchez',
    'email': '<EMAIL>',
    'age': 16,
  }];

  getData() {
    return this.data;
  }
}
