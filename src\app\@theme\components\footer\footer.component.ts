import { Component, OnInit } from '@angular/core'
import { FormBuilder } from '@angular/forms'
import { UserDataService } from '@common/services/user-data.service'
import { MenuService } from '@pages/menu.service'
import { distinctUntilChanged, filter, first, map, tap } from 'rxjs/operators'
import { FooterService } from './footer.service'
import { environment } from '@environments/environment'
import { EQP_APP_CONFIGURATION as eqpConfig } from '../../../../eqp-configuration'
import { LoginService } from '../../../auth/services/login.service'

@Component({
  selector: 'eqp-footer',
  styleUrls: ['./footer.component.scss'],
  templateUrl: './footer.component.html',
})
export class FooterComponent implements OnInit {
  public model = this._getModedl()

  public entities: any[] = []
  public exercises: any[] = []
  public urlTransparencia = ''
  public config = eqpConfig
  public userData: any

  constructor(
    private _builder: FormBuilder,
    private _userService: UserDataService,
    private _menuService: MenuService,
    private _footerService: FooterService,
    private _loginService: LoginService,
  ) {}

  ngOnInit(): void {
    this._getEntities()
    this._initialModelData()
    this._loadExercicio()
    this._getDomain()
    this._changeEntity()
    this._changeExercise()
  }

  private _getModedl() {
    return this._builder.group({
      entidadeUuid: [],
      exercicioUuid: [],
    })
  }

  private get _controls() {
    return this.model.controls
  }

  private _initialModelData() {
    this.userData = this._userService.getUserData()

    this._controls.entidadeUuid.patchValue(this.userData.entidade.uuid)
    this._controls.exercicioUuid.patchValue(this.userData.exercicioUuid)
  }

  private _getEntities(): void {
    this._menuService
      .getEntidades()
      .pipe(
        first(),
        map(res => {
          return res.data.map(data => {
            data.nome = `${data.codigo} - ${data.nome}`
            return data
          })
        }),
      )
      .subscribe((data: any) => {
        this.entities = data
      })
  }

  private _loadExercicio() {
    this._menuService
      .getExercicios()
      .pipe(first())
      .subscribe(data => {
        let exerc = []
        data.data.forEach(item => {
          if (item.exercicio >= 2013) {
            item.exercicio = item.exercicio + ' - ' + item.exercicioStatus.nome
            exerc.push(item)
          }
        })
        this.exercises = exerc
      })
  }

  private _getDomain() {
    const environmentNameAfterRefex = environment.url.match(/-([^.-]+)\./)
    const environmentDescription = environmentNameAfterRefex
      ? `-${environmentNameAfterRefex[1]}.ops.equiplano.com.br`
      : '.equiplano.cloud'
    this._footerService
      .getDomain()
      .pipe(first())
      .subscribe(res => {
        const domainName = res.dados.dominio
        if (domainName) {
          this.urlTransparencia = `https://portal-${domainName}${environmentDescription}`
        }
      })
  }

  private _changeEntity() {
    this._controls.entidadeUuid.valueChanges
      .pipe(
        distinctUntilChanged(),
        filter(value => value),
      )
      .subscribe(value => {
        this._controls.exercicioUuid.patchValue('')
        const entity = this.entities.find(item => item.uuid === value)
        this.userData.entidadeUuid = entity.uuid
        this.userData.clienteUuid = entity.clienteUuid
        this.userData.entidade = entity
      })
  }

  private _changeExercise() {
    this._controls.exercicioUuid.valueChanges
      .pipe(
        distinctUntilChanged(),
        filter(value => value),
      )
      .subscribe(value => {
        this.userData.exercicioUuid = value
        this.exercises.forEach(item => {
          if (this.userData.exercicioUuid === item.uuid) {
            this.userData.exercicio = item.exercicio.substring(0, 4)
            this.userData.exercise = item
            this.userData.exercise.exercicio = item.exercicio.substring(0, 4)
          }
        })
        this._changeSessionData()
      })
  }

  private _changeSessionData() {
    this._loginService
      .atualizarToken({
        tokenUuid: this.userData.idToken,
        exercicioUuid: this.userData.exercicioUuid,
        exercicioAno: this.userData.exercicio,
      })
      .pipe(first())
      .subscribe(data => {
        localStorage.removeItem('userData')
        this.userData.tokenJwt = data.dados
        localStorage.setItem('userData', JSON.stringify(this.userData))
        this._footerService
          .atualizaSessao(this.userData)
          .pipe(first())
          .subscribe(sessao => {
            localStorage.removeItem('userData')
            localStorage.setItem('userData', JSON.stringify(sessao.dados))
            window.location.reload()
          })
      })
  }
}
