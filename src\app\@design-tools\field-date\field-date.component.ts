import { Component, EventEmitter, Host, Input, OnInit, Optional, Output, SkipSelf } from '@angular/core';
import { Control<PERSON>ontainer, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import moment from 'moment';

type DateConfig = {
  inputFormat: string;
  outputFormat: string;
  displayFormat: string;
  maskRegex: any;
};

@Component({
  selector: 'eqp-field-date',
  templateUrl: './field-date.component.html',
  styleUrls: ['./field-date.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: FieldDateComponent,
    },
  ],
})
export class FieldDateComponent implements ControlValueAccessor {
  @Input() private formControlName: string
  @Input() disabled = false
  @Input() readonly = false
  @Input() required = false
  @Input() label = ''
  @Input() name = ''
  @Input() class = ''
  @Input() placeholder = '00/00/0000'

  currentValue: Date | string

  constructor(private controlContainer: ControlContainer) {}

  onChanged(value: any) {}

  onTouched: (value: any) => {}

  changeValue(value: any) {
    this.currentValue = value
    let newValue = value
      ? moment(value).utcOffset(0, true).format('YYYY-MM-DD')
      : null
    this.onChanged(newValue)
    this.onTouched(newValue)
  }

  writeValue(value: any) {
    this.currentValue = value ? moment(value, 'YYYY-MM-DD').toDate() : null
  }

  registerOnChange(fn: any): void {
    this.onChanged = fn
  }

  public getAbsControl(): any {
    if (!this.controlContainer) return null

    return this.controlContainer.control.get(this.formControlName)
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn
  }

  setDisabledState(isDisabled: boolean) {
    this.disabled = isDisabled
  }
}
