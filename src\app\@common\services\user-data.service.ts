import { Injectable } from '@angular/core'
import { Router } from '@angular/router'
import { interval } from 'rxjs'
import { first } from 'rxjs/operators'
import { LoginService } from '../../auth/services/login.service'

@Injectable({
  providedIn: 'root',
})
export class UserDataService {
  constructor(private service: LoginService, private router: Router) {
    interval(1000 * 10).subscribe(() => {
      this.validarSessao()
    })
  }

  public getUserData(): any {
    return JSON.parse(localStorage.getItem('userData'))
  }

  public getTheme(): string {
    const tema = JSON.parse(localStorage.getItem('userData'))
    return tema?.theme
  }

  public getThemeDev(): string {
    const tema = JSON.parse(localStorage.getItem('userData'))
    return tema?.devExtremeTheme
  }

  public atualizaSessao(user: any): void {
    this.service
      .gravarSessao(user)
      .pipe(first())
      .subscribe(data => {
        localStorage.removeItem('userData')
        localStorage.setItem('userData', JSON.stringify(data.dados))
      })
  }

  public validarSessao(): void {
    const sessaoLocal = JSON.parse(localStorage.getItem('userData'))
    if (sessaoLocal?.idToken) {
      this.service
        .recuperaSessao(sessaoLocal.idToken)
        .pipe(first())
        .subscribe(sessaoSalva => {
          if (
            sessaoSalva.dados.clienteUuid !== sessaoLocal.clienteUuid ||
            sessaoSalva.dados.entidadeUuid !== sessaoLocal.entidadeUuid ||
            sessaoSalva.dados.exercicioUuid !== sessaoLocal.exercicioUuid ||
            sessaoSalva.dados.municipioClienteUuid !==
              sessaoLocal.municipioClienteUuid ||
            sessaoSalva.dados.uuid !== sessaoLocal.uuid
          ) {
            localStorage.removeItem('userData')
            localStorage.setItem('userData', JSON.stringify(sessaoSalva.dados))
            window.location.reload()
          }
        })
    } else if (
      !location.href.includes('/login') &&
      !location.href.includes('/resetar-senha/')
    ) {
      this.router.navigate(['/login'])
    }
  }
}
