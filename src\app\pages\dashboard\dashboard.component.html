<div class="row">
  <div class="col-md-12">
    <nb-card
      [nbSpinner]="loading"
      [nbSpinnerStatus]="'info'"
      *ngIf="menus.length > 0"
    >
      <nb-card-header>
        <div class="row">
          <div class="col-md-8">
            <h6>Menus principais</h6>
          </div>
        </div>
      </nb-card-header>
      <nb-card-body>
        <div class="row">
          <div class="col-md-2 col-sm-3" *ngFor="let item of menus">
            <div class="row pointer" title="{{ item.title }}">
              <div
                class="col-md-12 mb-1 flex-action-item align-center"
                (click)="abrirRota(item)"
              >
                <img
                  class="icon-system"
                  [src]="
                    item.iconeTela
                      ? 'assets/menu-icone/' + item.iconeTela
                      : 'assets/icons-system/acesso nuvem.png'
                  "
                  alt="Logo"
                />
              </div>
              <div class="col-md-12" [ngStyle]="{ 'text-align': 'center' }">
                <span>{{ item.title }}</span>
              </div>
            </div>
          </div>
        </div>
      </nb-card-body>
    </nb-card>
  </div>
</div>
