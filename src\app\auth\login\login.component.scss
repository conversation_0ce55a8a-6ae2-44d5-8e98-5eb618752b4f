@import 'src/assets/scss/variables';

:host {
  .login {
    display: flex;
    width: 100%;
    height: 100vh;
    overflow: hidden;
  }

  .left-panel {
    position: relative;
    width: 70%; 
    height: 100%;

    @media (max-width: 991px) {
      display: none;
    }

    .bg-image {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .left-content {
      position: absolute;
      top: 15%;
      left: 10%;
      width: 80%;
      z-index: 1;

      h2 {
        color: white;
        font-size: 2.5rem;
        font-weight: 600;
        line-height: 1.2;
        margin-bottom: 2rem;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .right-panel {
    width: 30%;
    height: 100%;
    background-color: #1e3a5f;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; 
    padding: 2rem;
    transition: width 0.3s ease;

    &.full-width {
      width: 100%;
    }

    @media (max-width: 991px) {
      width: 100%;
    }

    .logo {
      width: 100%;
      display: flex;
      justify-content: center; 
      margin-bottom: 3rem;

      img {
        height: 150px; 
      }
    }
  }

  .login-content {
    width: 100%;
    max-width: 80%; // Reduzido para ficar mais centralizado

    h3 {
      font-size: 1.8rem;
      margin-bottom: 0.5rem;
      text-align: center; // Centraliza o título
    }

    .login-subtitle {
      margin-bottom: 1.5rem;
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.9rem;
      text-align: center; // Centraliza o subtítulo
    }

    .form-group {
      margin-bottom: 1.5rem;
      position: relative;

      label {
        display: block;
        margin-bottom: 0.3rem;
        font-size: 0.9rem;
      }

      .password-container {
        position: relative;
        width: 100%;
        
        .form-control {
          width: 100%;
          padding: 0.6rem 0.8rem;
          padding-right: 2.5rem;
          border-radius: 4px;
          border: none;
          background-color: rgba(255, 255, 255, 0.1);
          color: white;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(0, 214, 143, 0.5);
          }
        }
        
        .password-toggle {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          color: rgba(255, 255, 255, 0.7);
          cursor: pointer;
          transition: color 0.2s ease;
          
          &:hover {
            color: white;
          }
          
          i {
            font-size: 16px;
          }
        }
      }

      .forgot-link {
        position: absolute;
        right: 0;
        top: calc(100% + 5px);
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        text-decoration: none;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .btn-confirm {
      width: 100%;
      padding: 0.7rem;
      background-color: #00d68f;
      color: #1a2b50;
      border: none;
      border-radius: 4px;
      font-weight: bold;
      font-size: 1rem;
      cursor: pointer;
      margin-bottom: 1rem;
      transition: all 0.3s ease;
      
      &:hover {
        background-color: darken(#00d68f, 5%);
      }
      
      &:active {
        transform: translateY(1px);
      }
      
      &:disabled {
        background-color: rgba(0, 214, 143, 0.5);
        cursor: not-allowed;
      }
    }

    .btn-certificate {
      width: 100%;
      padding: 0.7rem;
      background-color: transparent;
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 4px;
      font-weight: bold;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      
      i {
        margin-right: 8px;
        font-size: 1rem;
      }
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);
      }
      
      &:active {
        transform: translateY(1px);
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .app-stores {
      display: flex;
      justify-content: center;
      margin-top: 1.5rem;
      gap: 0.8rem;

      .store-link img {
        height: 30px;
      }
    }

    .access-site {
      display: block;
      text-align: center;
      margin-top: 0.8rem;
      color: white;
      text-decoration: none;
      font-size: 0.8rem;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // Telas de seleção (município, entidade, exercício)
  .selection-content {
    width: 100%;
    max-width: 800px;

    .selection-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;

      h3 {
        color: #fff;
        font-size: 1.5rem;
        margin: 0;
      }

      .btn-back {
        background: none;
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }

    // Ajuste para a tela de exercício
    .d-flex {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
    }

    .entity-info {
      display: flex;
      align-items: center;
      margin-bottom: 2rem;
      justify-content: center;

      .item-image {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 1rem;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      h4 {
        margin: 0;
        color: #fff;
        font-size: 1.2rem;
      }
    }

    .exercise-selection {
      max-width: 400px;
      width: 100%;

      .form-group {
        margin-bottom: 1.5rem;

        label {
          display: block;
          margin-bottom: 0.5rem;
          font-size: 0.9rem;
        }

        .form-control {
          width: 100%;
          padding: 0.6rem 0.8rem;
          border-radius: 4px;
          border: none;
          background-color: rgba(255, 255, 255, 0.1);
          color: white;

          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(0, 214, 143, 0.5);
          }
        }
      }

      .btn-next {
        width: 100%;
        padding: 0.75rem;
        background-color: #00d68f; // Verde claro
        color: #1a2b50;
        border: none;
        border-radius: 4px;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        margin-top: 1rem;

        &:hover {
          background-color: darken(#00d68f, 5%);
        }

        &:disabled {
          background-color: rgba(0, 214, 143, 0.5);
          cursor: not-allowed;
        }
      }
    }
  }
}

// Remover estilos do Nebular que podem interferir
// Não remover o ':host', ele é necessário para não aplicar os estilos em elementos filhos.
:host ::ng-deep nb-layout-column {
  padding: 0 !important;
}

:host ::ng-deep nb-card {
  margin: 0 !important;
  border: none !important;
  box-shadow: none !important;
}

:host ::ng-deep nb-card-body {
  padding: 0 !important;
}

:host ::ng-deep nb-card-header {
  display: none !important;
}

// Estilo para o container de pesquisa
.search-container {
  margin-bottom: 1rem;
  width: 100%;

  ::ng-deep {
    nb-form-field {
      width: 100%;

      .nb-form-field-control {
        background-color: rgba(255, 255, 255, 0.1) !important;
        border-color: transparent !important;
        border-radius: 4px !important;
      }

      input {
        color: white !important;

        &::placeholder {
          color: rgba(255, 255, 255, 0.6) !important;
        }

        &:focus {
          box-shadow: 0 0 0 2px rgba(0, 214, 143, 0.5) !important;
        }
      }

      nb-icon[nbPrefix] {
        color: rgba(255, 255, 255, 0.6) !important;
        position: relative !important;
        top: 0 !important;
        left: 0 !important;
      }

      button[nbButton].appearance-ghost {
        color: rgba(255, 255, 255, 0.6) !important;

        &:hover {
          color: white !important;
        }
      }
    }
  }
}

// Estilo para a lista de seleção personalizada
.selection-list {
  width: 100%;
  max-height: 70vh;
  overflow-y: auto;

  // Estilização da barra de rolagem
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  .selection-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background-color: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: 8px;
    border-radius: 4px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .item-image {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.1);
      margin-right: 16px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }

      i {
        font-size: 20px;
        color: white;
      }
    }

    .item-info {
      h4 {
        color: white;
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

// Estilo para mensagem de nenhum resultado
.no-results {
  text-align: center;
  padding: 24px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;

  p {
    margin: 0;
    color: #ffff;
    font-size: 14px;
    opacity: 0.7;
  }
}

// Estilo para a tela de seleção
.selection-content {
  width: 100%;
  max-width: 800px;

  .selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h3 {
      color: #fff;
      font-size: 1.5rem;
      margin: 0;
    }

    .btn-back {
      background: none;
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

// Estilo para o campo de pesquisa personalizado
.custom-search-container {
  position: relative;
  margin-bottom: 1.5rem;
  width: 100%;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid transparent;

  &:focus-within {
    box-shadow: 0 0 0 2px rgba(0, 214, 143, 0.4);
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(0, 214, 143, 0.3);
  }

  .search-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
  }

  .custom-search-input {
    flex: 1;
    background: transparent;
    border: none;
    height: 48px;
    color: white;
    font-size: 16px;
    padding: 0;
    outline: none;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }
  }

  .clear-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    font-size: 14px;
    transition: color 0.2s ease;

    &:hover {
      color: white;
    }
  }
}
