{"rulesDirectory": ["node_modules/codelyzer"], "rules": {"deprecation": {"severity": "warning"}, "trailing-comma": [true, {"multiline": "always", "singleline": "never"}], "arrow-return-shorthand": true, "callable-types": true, "class-name": true, "comment-format": [true, "check-space"], "eofline": true, "forin": true, "import-blacklist": [true], "import-spacing": true, "indent": [true, "spaces"], "interface-over-type-literal": true, "label-position": true, "max-line-length": [true, 120], "member-access": false, "no-arg": true, "no-console": [true, "debug", "log", "time", "timeEnd", "trace"], "no-construct": true, "no-debugger": true, "no-duplicate-super": true, "no-empty": false, "no-empty-interface": true, "no-eval": true, "no-misused-new": true, "no-non-null-assertion": true, "no-shadowed-variable": true, "no-string-literal": false, "no-string-throw": true, "no-switch-case-fall-through": true, "no-trailing-whitespace": true, "no-unnecessary-initializer": true, "no-var-keyword": true, "object-literal-sort-keys": false, "one-line": [true, "check-open-brace", "check-catch", "check-else", "check-whitespace"], "prefer-const": true, "quotemark": [true, "single"], "radix": true, "semicolon": [true, "always"], "triple-equals": [true, "allow-null-check"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "typeof-compare": true, "unified-signatures": true, "variable-name": false, "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"], "directive-selector": [true, "attribute", "eqp", "camelCase"], "component-selector": [true, "element", "eqp", "kebab-case"], "ban": [true, "eval", "fit", "fdescribe", {"name": "$", "message": "please don't"}], "no-inputs-metadata-property": true, "no-outputs-metadata-property": true, "no-output-rename": true, "use-lifecycle-interface": true, "use-pipe-transform-interface": true, "component-class-suffix": true, "directive-class-suffix": true}}