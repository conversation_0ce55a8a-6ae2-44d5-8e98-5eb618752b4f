import { Component, OnDestroy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import {
  ModalConfirmarComponent,
} from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'

import { EmailConfigService } from '../email-config.service'

@Component({
  selector: 'eqp-email-config-list',
  templateUrl: './email-config-list.component.html',
  styleUrls: ['./email-config-list.component.scss'],
})
export class EmailConfigListComponent
  extends BaseTelasComponent
  implements OnInit, OnDestroy
{
  public loading: boolean = false
  public pageTitle: string = 'Administração | Configuração de envio de e-mail'

  public gridData: any

  private subscription: Subscription
  constructor(
    private service: EmailConfigService,
    private toastr: ToastrService,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
    this.permissao('/administracao/configuracao-email')
  }

  public ngOnInit(): void {
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      'configuracao_email',
      10,
    )
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items[0].showText = 'ever'
    event.toolbarOptions.items[0].options.text = 'Nova configuração'
    event.toolbarOptions.items[0].options.hint = 'Nova configuração de e-mail'

    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`administracao/configuracao-email/novo`])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([`administracao/configuracao-email/edit/${uuid}`])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Configuração de e-mail excluído com sucesso.',
              })
              this.gridData = this.service.getDataSourceFiltro(
                'uuid',
                'configuracao_email',
                10,
              )
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
