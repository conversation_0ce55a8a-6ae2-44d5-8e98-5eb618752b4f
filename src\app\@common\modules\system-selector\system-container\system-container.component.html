: <nb-card>
  <nb-card-header>
    <div class="row">
      <div class="col-md-12">
        <h5>
          {{ containerTitle }}
        </h5>
      </div>
    </div>
  </nb-card-header>
  <nb-card-body>
    <ng-container>
      <div class="row">
        <div
          class="col-md-4 mb-2 pointer"
          *ngFor="let module of modules"
        >
          <div
            class="row pointer"
            title="{{ module.descricao }}"
          >
            <div
              class="col-md-12 mb-1 flex-action-item align-center"
              (click)="switchModules(module)"
            >
              <img
                class="icon-system"
                [src]="module.icone ? 'assets/icons-system/'+module.icone : 'assets/images/laptop.svg'"
                alt="Logo"
                style="width: 60px; height: 60px;"
              >
            </div>
            <div
              class="col-md-12"
              [ngStyle]="{'text-align': 'center'}"
            >
              <span>{{ module.descricao }}</span>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </nb-card-body>
</nb-card>
