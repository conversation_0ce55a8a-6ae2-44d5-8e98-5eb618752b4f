{"version": "0.2.0", "configurations": [{"type": "chrome", "request": "launch", "name": "Launch Chrome against localhost", "url": "http://localhost:4201", "sourceMaps": true, "webRoot": "${workspaceRoot}"}, {"type": "chrome", "request": "attach", "name": "Attach to Chrome", "port": 9222, "sourceMaps": true, "webRoot": "${workspaceRoot}"}, {"name": "Docker Node.js Launch", "type": "docker", "request": "launch", "preLaunchTask": "docker-run: debug", "platform": "node"}]}