import { Component, OnInit } from '@angular/core'
import { FormB<PERSON>er, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { ActionsService } from '@pages/equiplano/access-control/actions/actions.service'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'
import { NewsletterService } from '../newsletter.service'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogService } from '@nebular/theme'
import { getMomentDate } from '@common/helpers/parsers'

@Component({
  selector: 'eqp-newsletter-form',
  templateUrl: './newsletter-form.component.html',
  styleUrls: ['./newsletter-form.component.scss'],
})
export class NewsletterFormComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading = false
  public pageTitle: string = 'Equiplano | Boletim Informativo'
  public model = this._obterModelo()
  public modules: any

  constructor(
    private _builder: FormBuilder,
    private _router: Router,
    private _toastr: ToastrService,
    private _actionsService: ActionsService,
    private _service: NewsletterService,
    private _dialogService: NbDialogService,
    private _route: ActivatedRoute,
    public meuService: MenuService,
  ) {
    super(meuService)
  }

  ngOnInit(): void {
    this._obterModulos()
    this._carregarDadosIniciais()
  }

  private _obterModelo() {
    return this._builder.group({
      uuid: [''],
      titulo: ['', Validators.required],
      moduloUuid: ['', Validators.required],
      data: ['', Validators.required],
      descricao: ['', Validators.required],
    })
  }

  private _sugerirData() {
    const data = new Date();
    const dataMoment = getMomentDate(data.toString()).format('YYYY-MM-DDTHH:mm:ss')
    this.model.get('data').patchValue(dataMoment);
  }

  private get _controles() {
    return this.model.controls
  }

  public voltar() {
    this.gravarParametros()
    this._router.navigate([`boletim-informativo`])
  }

  public confirmar() {
    if (this.model.invalid) {
      this._toastr.send({
        error: true,
        message: 'Por favor, preencha os campos obrigatórios',
      })
      this.model.markAllAsTouched()
    } else {
      // this.loading = true
      if (this.estaEditando()) {
        this._atualizar()
      } else {
        this._criar()
      }
    }
  }

  private _criar() {
    console.log(
      this.model.getRawValue()
    )
    
    this._service
      .post(this.model.getRawValue())
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(() =>
        this._logicaSucessoRequisicao('Boletim informativo criado com sucesso.'),
      )
  }

  private _atualizar() {
    this._service
      .put(this.model.getRawValue())
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(() =>
        this._logicaSucessoRequisicao(
          'Boletim informativo atualizado com sucesso.',
        ),
      )
  }

  public excluir() {
    const dialogRef = this._dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.loading = true
        this._service
          .delete(this._controles.uuid.value)
          .pipe(
            first(),
            finalize(() => (this.loading = false)),
          )
          .subscribe(() =>
            this._logicaSucessoRequisicao(
              'Boletim informativo excluído com sucesso.',
            ),
          )
      }
    })
  }

  public estaEditando() {
    return !!this._controles.uuid.value
  }

  public get exibirErroDescricao() {
    const descricaoControl = this._controles.descricao
    return (
      descricaoControl?.errors?.required &&
      (descricaoControl?.touched || !descricaoControl?.pristine)
    )
  }

  private _obterModulos(): void {
    this.modules = new DataSource({
      store: this._actionsService.getDataSourceFiltro(
        'uuid',
        'boletim-informativo/modulos',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  private _logicaSucessoRequisicao(mensagem: string) {
    this._toastr.send({
      success: true,
      message: mensagem,
    })
    this.voltar()
  }

  private _carregarDadosIniciais() {
    this._route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) this._obterDados(uuid)
      else this._sugerirData()
    })
  }

  private _obterDados(uuid: string) {
    this.loading = true
    this._service
      .getByUiid(uuid)
      .pipe(
        first(),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        const boletim = res.dados
        if (boletim.publicar === 'S') {
          this.voltar()
          this._toastr.send({
            error: true,
            message: 'Não é possível editar um boletim publicado',
          })
        }
        this.model.patchValue(boletim)
      })
  }
}
