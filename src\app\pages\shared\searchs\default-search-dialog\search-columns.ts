import { DefaultColumnSearchInterface } from '@pages/shared/interfaces/default-search';

interface SearchColumnsInterface {
  [campo: string]: DefaultColumnSearchInterface[];
}
export const SEARCH_COLUMNS: SearchColumnsInterface = {
  userColumns: [
    {
      caption: 'Nome',
      dataField: 'nome',
    },
    {
      caption: 'Email',
      dataField: 'email',
    },
    {
      caption: 'Cpf',
      dataField: 'cpf',
    },
  ],
};
