<eqp-standard-page mainTitle="Boletim Informativo">
  <div class="container">
    <!-- Campo de pesquisa -->
    <div class="search-container mb-3">
      <div class="search-input-wrapper">
        <input
          type="text"
          class="search-input"
          placeholder="Pesquisar boletins por título..."
          [(ngModel)]="termoPesquisa"
          (input)="onPesquisaChange($event.target.value)"
        />
        <button
          *ngIf="termoPesquisa"
          class="clear-search-btn"
          (click)="limparPesquisa()"
          title="Limpar pesquisa"
        >
          <i class="fas fa-times"></i>
        </button>
        <i class="fas fa-search search-icon"></i>
      </div>
    </div>

    <ng-container *ngFor="let modulo of modulos">
      <nb-accordion id="accordion-{{ modulo.uuid }}" class="mb-1">
        <nb-accordion-item #accordionItem>
          <nb-accordion-item-header>
            {{ modulo.descricao }}
          </nb-accordion-item-header>
          <nb-accordion-item-body>
            <ng-container *ngIf="accordionItem.expanded">
              <eqp-newsletter-view-description
                [modulo]="modulo"
                [termoPesquisa$]="termoPesquisa$"
              ></eqp-newsletter-view-description>
            </ng-container>
          </nb-accordion-item-body>
        </nb-accordion-item>
      </nb-accordion>
    </ng-container>
  </div>
</eqp-standard-page>
