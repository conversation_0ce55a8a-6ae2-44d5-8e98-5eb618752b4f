<eqp-standard-page mainTitle="Boletim Informativo">
  <div class="container">
    <div class="search-container">
      <div class="search-input-wrapper">
        <input
          type="text"
          class="search-input"
          placeholder="Pesquisar módulos"
          [(ngModel)]="termoPesquisaModulo"
          (input)="onPesquisaModuloChange($event.target.value)"
        />
        <button
          *ngIf="termoPesquisaModulo"
          class="clear-search-btn"
          (click)="limparPesquisaModulo()"
          title="Limpar pesquisa"
        >
          <i class="fas fa-times"></i>
        </button>
        <i class="fas fa-search search-icon"></i>
      </div>
    </div>

    <ng-container *ngIf="modulosFiltrados.length > 0; else nenhumModuloEncontrado">
      <ng-container *ngFor="let modulo of modulosFiltrados">
        <nb-accordion id="accordion-{{ modulo.uuid }}" class="mb-1">
          <nb-accordion-item #accordionItem (collapsedChange)="onAccordionChange($event)">
            <nb-accordion-item-header>
              {{ modulo.descricao }}
            </nb-accordion-item-header>
            <nb-accordion-item-body>
              <ng-container *ngIf="accordionItem.expanded">
                <eqp-newsletter-view-description
                  [modulo]="modulo"
                ></eqp-newsletter-view-description>
              </ng-container>
            </nb-accordion-item-body>
          </nb-accordion-item>
        </nb-accordion>
      </ng-container>
    </ng-container>

    <!-- Mensagem quando não há módulos encontrados -->
    <ng-template #nenhumModuloEncontrado>
      <div class="no-modules-found">
        <div class="no-modules-content">
          <i class="fas fa-search"></i>
          <h4>Nenhum módulo encontrado</h4>
          <p>Não foram encontrados módulos que correspondam à sua pesquisa.</p>
          <button
            class="btn btn-outline-primary"
            (click)="limparPesquisaModulo()"
          >
            Limpar pesquisa
          </button>
        </div>
      </div>
    </ng-template>
  </div>
</eqp-standard-page>
