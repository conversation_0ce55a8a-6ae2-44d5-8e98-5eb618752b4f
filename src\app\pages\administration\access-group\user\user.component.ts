import { Component, Input, OnInit } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'
import DataSource from 'devextreme/data/data_source'

import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogService } from '@nebular/theme'
import { first } from 'rxjs/operators'
import { UsuarioListaComponent } from '../usuario-lista/usuario-lista.component'
import { AccessGroupService } from './../access-group.service'

@Component({
  selector: 'eqp-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.scss'],
})
export class UserComponent implements OnInit {
  @Input()
  public accessGroup: any

  @Input()
  public nivelPermissao: string

  public gridData: any
  public loading: boolean = false

  constructor(
    private service: AccessGroupService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {}

  public ngOnInit(): void {
    this.fetchGrid()
  }

  private fetchGrid(): void {
    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        `grupo_acesso/usuario/${this.accessGroup.uuid}`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.name === 'addRowButton') {
        item.showText = 'ever'
        item.options.text = 'Novo usuário'
        item.options.hint = 'Novo usuário'
        item.options.onClick = () => this.novoRegistro()
      }

      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    const dialogRef = this.dialogService.open(UsuarioListaComponent, {
      context: {
        grupoAcesso: this.accessGroup,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno.length > 0) {
        this.service
          .postUsuario(retorno, this.accessGroup.uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Grupo de acesso do usuário incluido com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .deleteUsuario(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Grupo de acesso do usuário excluído com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
