import { AfterViewInit, Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelect } from '@design-tools/interfaces/nebular-select'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import {
  debounceTime,
  distinctUntilChanged,
  finalize,
  first,
} from 'rxjs/operators'

import { AccessGroupService } from './../access-group.service'

@Component({
  selector: 'eqp-access-group',
  templateUrl: './access-group.component.html',
  styleUrls: ['./access-group.component.scss'],
})
export class AccessGroupComponent
  extends BaseTelasComponent
  implements OnInit, AfterViewInit
{
  public loading: boolean = false
  public pageTitle: string = 'Controle de acesso | Grupo de acesso'
  public formulario: FormGroup

  public clientesData: any
  public moduloData: any
  public permissaoData: any
  public nivelAcessoData: any

  public statuses: NebularSelect[] = [
    {
      texto: 'Ativo',
      valor: 'ACTIVE',
    },
    {
      texto: 'Inativo',
      valor: 'INACTIVE',
    },
    {
      texto: 'Bloqueado',
      valor: 'BLOCKED',
    },
  ]

  constructor(
    private service: AccessGroupService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
    this.permissao('/equiplano/controle-acesso/grupo-acesso')
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.setFocus(), 1000
    })
  }

  private setFocus(): void {
    document.getElementById('Nome').focus()
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      accessGroup: this.formBuilder.group({
        uuid: [''],
        nome: ['', [Validators.required, Validators.maxLength(250)]],
        descricao: ['', [Validators.required, Validators.maxLength(250)]],
        status: ['ACTIVE', [Validators.required]],
        clienteUuid: ['', [Validators.required]],
        moduloUuid: ['', [Validators.required]],
        nivelAcesso: ['', [Validators.required]],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) this.buscar(uuid)
      this.loadSelects()
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('accessGroup').patchValue(data.dados)

        this.service
          .getNivelAcesso(data.dados.moduloUuid)
          .pipe(first())
          .subscribe(data => {
            this.nivelAcessoData = data.data
          })
      })
  }

  public cancelar(): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/controle-acesso/grupo-acesso`])
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.loading = true
        this.service
          .delete(this.formulario.get('accessGroup.uuid').value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Grupo de acesso excluída com sucesso.',
              })
              this.cancelar()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Falta preencher os campos obrigatórios',
      })
      this.formulario.markAllAsTouched()
    } else {
      this.loading = true
      if (this.formulario.get('accessGroup.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getGrupoAcessoDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Grupo de acesso criado com sucesso.',
        })
        this.cancelar()
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getGrupoAcessoDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Grupo de acesso atualizado com sucesso.',
        })
        this.cancelar()
      })
  }

  private getGrupoAcessoDto(): any {
    const dto = this.formulario.getRawValue()

    return dto.accessGroup
  }

  private loadSelects(): void {
    this.clientesData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'grupo_acesso/cliente',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })

    this.moduloData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        'grupo_acesso/module',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })

    this.changeModule()
  }

  private changeModule(): void {
    this.formulario
      .get('accessGroup.moduloUuid')
      .valueChanges.pipe(distinctUntilChanged(), debounceTime(500))
      .subscribe(value => {
        this.formulario.get('accessGroup.nivelAcesso').patchValue('')
        if (value) {
          this.service
            .getNivelAcesso(value)
            .pipe(first())
            .subscribe(data => {
              this.nivelAcessoData = data.data
            })
        } else {
          this.nivelAcessoData = []
        }
      })
  }
}
