<eqp-standard-page
  [mainTitle]="pageTitle"
  [spinnerActive]="loading"
  [spinnerStatus]="'info'"
  [rightCustomButtonVisible]="true"
  [rightCustomButtonText]="'Alterar Senha'"
  [rightCustomButtonId]="'Alterar Senha'"
  [rightCustomButtonTitle]="'Alterar Senha'"
  (rightCustomButtonEmitter)="alterPassword()"
>
  <ng-container>
    <form [formGroup]="model">
      <div class="row" formGroupName="dados">
        <div class="col-6 col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="nome"
            name="Nome"
            label="Nome"
            placeholder="Nome"
            required
            errorMessage="É obrigatório preencher o nome"
            [maxlength]="100"
            [disabled]="true"
          >
          </eqp-nebular-input>
        </div>

        <div class="col-6 col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="email"
            name="e-mail"
            label="e-mail"
            placeholder="<EMAIL>"
            [maxlength]="100"
            required
            errorMessage="É obrigatório preencher o e-mail"
            [disabled]="true"
          >
          </eqp-nebular-input>
        </div>

        <div class="col-6 col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="cpf"
            name="CPF"
            label="CPF"
            placeholder="000.000.000-00"
            required
            errorMessage="É obrigatório preencher o CPF"
            primaryMask="000.000.000-00"
            [class]="'form-control'"
            [disabled]="true"
          >
          </eqp-nebular-input>
        </div>

        <div class="col-6 col-md-4">
          <eqp-nebular-select
            [selectValues]="statuses"
            [hasFullWidth]="true"
            [size]="'small'"
            formControlName="status"
            name="Status"
            label="Status"
            [disabled]="true"
          >
          </eqp-nebular-select>
        </div>

        <div class="col-6 col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="criadoEm"
            name="Criado Em"
            label="Criado Em"
            placeholder="00/00/0000"
            [disabled]="true"
          >
          </eqp-nebular-input>
        </div>

        <div class="col-6 col-md-4">
          <eqp-nebular-input
            [style]="'basic'"
            [size]="'small'"
            [shape]="'rectangle'"
            formControlName="atualizadoEm"
            name="Ultima atualização em"
            label="Ultima atualização em"
            placeholder="00/00/0000"
            [disabled]="true"
          >
          </eqp-nebular-input>
        </div>
      </div>
    </form>
  </ng-container>
</eqp-standard-page>
