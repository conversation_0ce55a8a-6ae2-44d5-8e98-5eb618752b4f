<eqp-nebular-dialog
  [dialogTitle]="'Selecionar | ' + searchData.dialogTitle"
  [spinnerStatus]="'info'"
  [bottomLeftButtonText]="'Voltar'"
  [bottomLeftButtonVisible]="true"
  (bottomLeftButtonEmitter)="cancel()"
  [rightFirstButtonVisible]="true"
  [rightFirstButtonIconVisible]="false"
  [rightFirstButtonText]="'Confirmar'"
  [rightFirstButtonIcon]="'fas fa-check'"
  [rightFirstButtonId]="'submit-contract-selection'"
  [rightFirstButtonDisabled]="selectedRowKeys.length == 0"
  (rightFirstButtonEmitter)="confirm()"
>
  <ng-container>
    <!-- <eqp-loading
      *ngIf="loading"
      class="loading"
      textLoading="Carregando dados..."
   ></eqp-loading> -->
    <dx-data-grid
      [dateSerializationFormat]="'yyyy-MM-dd'"
      id="contractGrid"
      [dataSource]="objIsData ? dataSource : dataSourceArray"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onSelectionChanged)="onSelectionChanged($event)"
    >
      <dxo-state-storing
        [enabled]="true"
        type="custom"
        [customLoad]="loadState"
        [customSave]="saveState"
        savingTimeout="100"
      ></dxo-state-storing>

      <dxo-selection
        selectAllMode="allPages"
        showCheckBoxesMode="onClick"
        [mode]="searchData.isMultiple ? 'multiple' : 'single'"
      ></dxo-selection>

      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="false"
        [placeholder]=""
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="true"
      >
      </dxo-editing>

      <dxi-column
        *ngFor="let customColumn of searchData.columns"
        [dataField]="customColumn?.dataField"
        [dataType]="customColumn?.dataType"
        [caption]="customColumn?.caption"
        [format]="customColumn?.format"
        [alignment]="customColumn?.childs ? 'center' : 'left'"
        [allowSorting]="customColumn?.allowSorting"
        [allowFiltering]="customColumn?.allowFiltering"
        [editorOptions]="customColumn?.editorOptions"
        [width]="customColumn?.width"
      >
        <dxo-lookup
          *ngIf="customColumn?.lookup !== undefined"
          [dataSource]="
            customColumn.lookup.dataSource
              ? customColumn?.lookup?.dataSource
              : dataSourceLookup
          "
          [valueExpr]="customColumn?.lookup?.valueExpr"
          [displayExpr]="customColumn?.lookup?.displayExpr"
        ></dxo-lookup>
      </dxi-column>
    </dx-data-grid>
  </ng-container>
</eqp-nebular-dialog>
