import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import {
  LicensesInterface,
  LicensesReturnInterface,
} from '@pages/equiplano/access-control/licenses/licenses'
import { Observable } from 'rxjs'

import { ModulesReturnInterface } from '../modules/modules'

@Injectable({
  providedIn: 'root',
})
export class LicensesService extends CrudService {
  constructor(private http: HttpClient) {
    super(http)
  }

  public get(): Observable<LicensesReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<LicensesReturnInterface>(`licenca`, {
      headers,
    })
  }

  public getModulos(): Observable<ModulesReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ModulesReturnInterface>(`licenca/modulos`, {
      headers,
    })
  }

  public getIndividual(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`licenca/${uuid}`, {
      headers,
    })
  }

  public put(dto: LicensesInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<LicensesInterface>(`licenca/${dto.uuid}`, dto, {
      headers,
      observe: 'response',
    })
  }

  public post(dto: LicensesInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<LicensesInterface>(`licenca`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: LicensesInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<LicensesInterface[]>(`licenca/lote`, batch, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`licenca/${uuid}`, {
      headers,
    })
  }
}
