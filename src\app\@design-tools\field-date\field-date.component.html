<div class="form-control-group">
  <label
    class="label"
    *ngIf="label"
    [attr.aria-label]="label"
    for="{{ name }}"
    >{{ label }}</label
  >
  <div class="position-relative input-group">
    <div
      nbInput
      fieldSize="small"
      class="input-full-width size-small status-basic shape-rectangle ng-untouched ng-pristine ng-valid ng-star-inserted nb-transition"
      (click)="dateInput.opened = !disabled && !readonly"
    >
      <dx-date-box
        #dateInput
        [class]="class + ' p-0 m-0'"
        [placeholder]="placeholder"
        type="date"
        pickerType="list"
        stylingMode="filled"
        [useMaskBehavior]="true"
        displayFormat="dd/MM/yyyy"
        [disabled]="disabled"
        [inputAttr]="{ placeholder: '' }"
        [readOnly]="readonly"
        [required]="required"
        [(ngModel)]="currentValue"
        (ngModelChange)="changeValue(currentValue)"
      >
      </dx-date-box>
    </div>
    <nb-icon
      [options]="{ animation: { type: 'pulse' } }"
      icon="calendar"
      status="info"
      class="absolute-right pointer calendar-accessor"
      (click)="dateInput.opened = !disabled && !readonly"
    ></nb-icon>
  </div>
  <div
    *ngIf="
      getAbsControl()?.errors?.customError &&
      (getAbsControl()?.touched || !getAbsControl()?.pristine)
    "
    class="invalid-feedback"
    [ngClass]="{
      'd-block': getAbsControl()?.errors
    }"
  >
    <div>{{ getAbsControl()?.errors?.customError }}</div>
  </div>
</div>
