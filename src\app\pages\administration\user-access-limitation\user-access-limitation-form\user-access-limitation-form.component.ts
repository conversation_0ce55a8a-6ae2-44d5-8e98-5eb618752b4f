import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { UserDialogComponent } from '@common/dialogs/user-dialog/user-dialog.component';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { CrudService } from '@common/services/crud.service';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogRef, NbDialogService } from '@nebular/theme';
import { UsersService } from '@pages/administration/users/users.service';
import { MenuService } from '@pages/menu.service';
import DataSource from 'devextreme/data/data_source';
import { finalize, first, take } from 'rxjs/operators';

@Component({
  selector: 'eqp-user-access-limitation-form',
  templateUrl: './user-access-limitation-form.component.html',
  styleUrls: ['./user-access-limitation-form.component.scss']
})
export class UserAccessLimitationFormComponent extends BaseTelasComponent implements OnInit {
  pageTitle = 'Limitação de Acesso';
  loading: boolean = false;
  form: FormGroup;

  uuid: string;

  orgao: DataSource;
  unidade: DataSource;

  constructor(
    private service: UsersService,
    private crudService: CrudService,
    private builder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
    this.permissao('/administracao/limitacao-acesso/novo')
  }

  ngOnInit(): void {
    this.form = this.builder.group({
      uuid: [],
      usuarioUuid: [undefined, [Validators.required]],
      orgaoUuid: [undefined, [Validators.required]],
      unidadeUuid: [undefined],
    });
    this.loadScreen();
    this.changeOrgao();
  }

  private loadScreen(): void {
    this.route.params.pipe(first()).subscribe((params) => {
      const uuid = params['uuid'];
      this.uuid = uuid;
      if (uuid) this.searchById(uuid);
      else this.loadSelects();
    });
  }

  private loadSelects() {
    this.orgao = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        `limitacao_acesso_orgao/orgao`,
        10,
        ),
      map: (item) => {
        return {
          ...item,
          nome: item.codigo + ' - ' + item.nome,
        };
      },
        paginate: true,
        pageSize: 10,
    });

    this.unidade = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        `limitacao_acesso_orgao/unidade`,
        10,
        'orgao', this.form.get('orgaoUuid').value
      ),
      map: (item) => {
        return {
          ...item,
          nome: item.codigo + ' - ' + item.nome,
        };
      },
      paginate: true,
      pageSize: 10,
    });
  }

  changeOrgao() {
    this.form.get('orgaoUuid').valueChanges.subscribe((value) => {
      this.unidade = new DataSource({
        store: this.service.getDataSourceFiltro(
          'uuid',
          `limitacao_acesso_orgao/unidade`,
          10,
          'orgao', value
        ),
        map: (item) => {
          return {
            ...item,
            nome: item.codigo + ' - ' + item.nome,
          };
        },
        paginate: true,
        pageSize: 10,
      });
    })
  }
  
  private searchById(uuid: string): void {
    this.loading = true;
   this.service
      .userAceessLimitation(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false;
        }),
      )
     .subscribe((res: any) => {
        this.form.patchValue({
          ...res.dados,
          usuarioUuid: {
            uuid: res.dados.usuario.uuid,
            nome: res.dados?.usuario?.nome,
            cpf: res.dados?.usuario?.cpf,
          },
          orgaoUuid: res.dados?.orgao?.uuid,
          unidadeUuid: res.dados?.unidade?.uuid,
        });
        this.loadSelects();
      });
  }

  create(dto: any) {
    this.service
      .createUserAceessLimitation(dto)
      .pipe(take(1), finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Limitação cadastrada com sucesso.',
          });
          this.cancel();
        },
        (err: any) => this.toastr.bulkSend(err.mensagens),
      );
  }

  update(dto: any) {
     this.service
      .updateUserAceessLimitation(dto)
      .pipe(take(1), finalize(() => (this.loading = false)))
      .subscribe(
        () => {
          this.toastr.send({
            success: true,
            message: 'Limitação atualizada com sucesso.',
          });
          this.cancel();
        },
        (err: any) => this.toastr.bulkSend(err.mensagens),
      );
  }

  cancel() {
    this.router.navigate(['administracao/limitacao-acesso']);
  }

  public onUserSearchDialog() {
    this.dialogService
      .open(UserDialogComponent, {
        context: {
          uri: 'limitacao_acesso_orgao/usuario',
        },
      })
      .onClose.pipe(first())
      .subscribe((res) => {
        if (res) {
          this.form.get('usuarioUuid').patchValue({
            uuid: res.uuid,
            cpf: res.cpf,
            nome: res.nome,

          })
        }
      });
  }

  public onUserSearchInput(value) {
    if (!value) {
      this.form.get('usuarioUuid').reset(undefined, { emitEvent: false });
      return;
    }
    this.loading = true;
    this.crudService
      .getDataSourceFiltro(
        'uuid',
        'limitacao_acesso_orgao/usuario',
        10,
        'cpf',
        `${value}`,
      )
      .load()
      .then((res) => {
        this.loading = false;
        if (res.length == 0) {
          this.toastr.send({
            error: true,
            message: 'Usuário não encontrado.',
          });
          this.form.get('usuarioUuid').reset(undefined, { emitEvent: false });
          return;
        }
        this.form.get('usuarioUuid').patchValue(res[0], { emitEvent: false });
      });
  }

  submit({ value, valid }: { value: any; valid: boolean }) {
    if (!valid) {
      return;
    }

    value.usuarioUuid = value.usuarioUuid.uuid;

    if (this.uuid) {
      this.update(value);
    } else {
      this.create(value);
    }
  }
}
