import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { IconeFormComponent } from './icone-form/icone-form.component'
import { IconeListagemComponent } from './icone-listagem/icone-listagem.component'
import { IconeComponent } from './icone.component'

const routes: Routes = [
  {
    path: '',
    component: IconeComponent,
    children: [
      {
        path: '',
        component: IconeListagemComponent,
      },
      {
        path: 'novo',
        component: IconeFormComponent,
      },
      {
        path: 'edit/:uuid',
        component: IconeFormComponent,
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class IconeRoutingModule {}
