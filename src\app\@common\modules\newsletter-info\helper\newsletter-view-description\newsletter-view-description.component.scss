.boletim-container {
  display: flex;
  flex-direction: column;
  gap: 0;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.boletim-item {
  margin-bottom: 0;
}

.view-button {
  display: flex;
  align-self: center;
  justify-self: center;
  height: 30px !important;
}

.boletim-content {
  background: var(--background-basic-color-1);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
  gap: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  min-width: 60px;
  font-size: 14px;
}

.value {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  .boletim-container {
    padding: 10px;
  }

  .boletim-content {
    padding: 15px;
  }

  .info-row {
    flex-direction: column;
    gap: 4px;
  }

  .label {
    min-width: auto;
  }
}

.boletim-item:last-child {
  margin-bottom: 0;
}

.boletim-item {
  animation: fadeInUp 0.3s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.boletim-item:nth-child(1) {
  animation-delay: 0.1s;
}
.boletim-item:nth-child(2) {
  animation-delay: 0.2s;
}
.boletim-item:nth-child(3) {
  animation-delay: 0.3s;
}
.boletim-item:nth-child(4) {
  animation-delay: 0.4s;
}
.boletim-item:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.not-found-newsletters {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 1.5rem !important;
  font-weight: bold;

  & p {
    margin-bottom: 0;
    margin-right: 5px;
  }
}


.pagination-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-family: Arial, sans-serif;
}

.pagination-info {
  font-size: 14px;
  color: #555;
}

.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.page-btn,
.page-nav {
  background: none;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease-in-out;
}

.page-btn.active {
  background-color: #00b0ff; /* Azul da imagem */
  color: white;
  font-weight: bold;
}

.page-btn:hover:not(.active),
.page-nav:hover:enabled {
  background-color: #e0e0e0;
}

.page-nav[disabled] {
  opacity: 0.3;
  cursor: not-allowed;
}
