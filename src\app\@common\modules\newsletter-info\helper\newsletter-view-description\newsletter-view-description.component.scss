/* Importar estilos compartilhados de pesquisa */
@import '../../styles/search-input.scss';

.boletim-container {
  display: flex;
  flex-direction: column;
  gap: 0;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.boletim-item {
  margin-bottom: 0;
}

.view-button {
  display: flex;
  align-self: center;
  justify-self: center;
  height: 30px !important;
}

.boletim-content {
  background: var(--background-basic-color-1);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
  gap: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  min-width: 60px;
  font-size: 14px;
}

.value {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  .boletim-container {
    padding: 10px;
  }

  .boletim-content {
    padding: 15px;
  }

  .info-row {
    flex-direction: column;
    gap: 4px;
  }

  .label {
    min-width: auto;
  }
}

.boletim-item:last-child {
  margin-bottom: 0;
}

.boletim-item {
  animation: fadeInUp 0.3s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.boletim-item:nth-child(1) {
  animation-delay: 0.1s;
}
.boletim-item:nth-child(2) {
  animation-delay: 0.2s;
}
.boletim-item:nth-child(3) {
  animation-delay: 0.3s;
}
.boletim-item:nth-child(4) {
  animation-delay: 0.4s;
}
.boletim-item:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.not-found-newsletters {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 1.5rem !important;
  font-weight: bold;

  & p {
    margin-bottom: 0;
    margin-right: 5px;
  }
}


/* Estilos da Paginação */
.pagination-container {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1rem;
  background: var(--background-basic-color-1);
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Seletor de itens por página */
.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-basic-color);
  }

  .page-size-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: var(--background-basic-color-1);
    color: var(--text-basic-color);
    font-size: 14px;
    cursor: pointer;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
  }
}

.pagination-page-block {
  display: flex;
  width: 100%;
  justify-content: end;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.25rem;
}

/* Informações da paginação */
.pagination-info {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--text-hint-color);
  text-align: center;
}

/* Controles de navegação */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: 0.5rem;
  border: 1px solid #ddd;
  background: var(--background-basic-color-1);
  color: var(--text-basic-color);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover:not(:disabled):not(.active) {
    background: var(--background-basic-color-2);
    border-color: #007bff;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--background-basic-color-3);
  }

  &.page-number {
    font-weight: 500;

    &.active {
      background: #007bff;
      color: white;
      border-color: #007bff;
      font-weight: 600;
    }
  }

  i {
    font-size: 12px;
  }
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  color: var(--text-hint-color);
  font-weight: bold;
}

/* Responsividade */
@media (max-width: 768px) {
  .pagination-container {
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .page-size-selector {
    justify-content: center;
    flex-wrap: wrap;
  }

  .pagination-controls {
    gap: 0.125rem;
  }

  .pagination-btn {
    min-width: 32px;
    height: 32px;
    font-size: 12px;

    i {
      font-size: 10px;
    }
  }

  .pagination-ellipsis {
    min-width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .pagination-info {
    font-size: 12px;
  }

  .page-size-selector label {
    font-size: 12px;
  }

  .page-size-select {
    font-size: 12px;
  }
}
