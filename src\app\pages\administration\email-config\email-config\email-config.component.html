<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
    <div class="mt-2">
      <eqp-breadcrumb></eqp-breadcrumb>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="emailConfig">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="smtpEndereco"
          name="Servidor de saída SMTP"
          label="Servidor de saída SMTP"
          placeholder="Servidor de saída SMTP"
          required="true"
          errorMessage="É obrigatório preencher o servidor de saída SMTP"
          maxlength="100"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          primaryMask="099999"
          formControlName="smtpPorta"
          name="Porta do SMTP"
          label="Porta do SMTP"
          placeholder="Porta do SMTP"
          required="true"
          errorMessage="É obrigatório preencher a porta do SMTP"
          maxlength="100"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="smtpUsuario"
          name="Usuário do SMTP"
          label="Usuário do SMTP"
          placeholder="Usuário do SMTP"
          required="true"
          errorMessage="É obrigatório preencher o usuário do SMTP"
          maxlength="100"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          type="password"
          formControlName="smtpSenha"
          name="Senha do SMTP"
          label="Senha do SMTP"
          placeholder="Senha do SMTP"
          required="true"
          errorMessage="É obrigatório preencher a senha do SMTP"
          maxlength="100"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Entidade"
          label="Entidade"
          placeholder="Entidade"
          formControlName="clienteUuid"
          [dataSource]="clientesData"
          valueExpr="uuid"
          displayExpr="nome"
          required="true"
        ></eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="
            formulario.get('emailConfig.uuid').value &&
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          *ngIf="
            (formulario.get('emailConfig.uuid').value &&
              nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
