import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelect } from '@design-tools/interfaces/nebular-select'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { finalize, first } from 'rxjs/operators'

import { ModulesService } from './../modules.service'

@Component({
  selector: 'eqp-modules',
  templateUrl: './modules.component.html',
  styleUrls: ['./modules.component.scss'],
})
export class ModulesComponent extends BaseTelasComponent implements OnInit {
  public loading: boolean = false
  public pageTitle: string = 'Controle de acesso | Módulos'
  public formulario: FormGroup

  public statuses: any = [
    { chave: 'ACTIVE', valor: 'Ativo' },
    { chave: 'INACTIVE', valor: 'Inativo' },
  ]
  public sistemas: any = [
    { chave: '45f7bc3d-3aaa-2082-0abd-dd6ff6b063c1', valor: 'Central' },
    { chave: '0a87103a-0665-8419-cd7d-56f054e3bfd8', valor: 'Cadastros' },
    { chave: '021e4fcb-c718-470e-b96f-1ba93e731394', valor: 'Compras' },
    { chave: '860a209e-cf05-a3f4-1375-09c4a156ccd3', valor: 'Contábil' },
    { chave: '8fb116b9-3932-13d3-8109-3aaec8d5983a', valor: 'Contrato' },
    {
      chave: '21c5dc81-4726-e81c-5631-99ccd55d2239',
      valor: 'Controle de dotação',
    },
    {
      chave: '09c59aea-60dc-e56a-6c0b-1877abf9e2dc',
      valor: 'Controle Interno',
    },
    {
      chave: '91a3dd85-c3f1-e139-ae93-e00504580608',
      valor: 'Execução Orçamentária',
    },
    {
      chave: '8ba23d09-0ae9-cde1-0b3b-3cbffc2b5a8b',
      valor: 'Leis & Atos',
    },
    {
      chave: 'bf5c0706-25c2-1e19-d013-6fbb1026e85c',
      valor: 'Licitação',
    },
    {
      chave: '759cc6b6-585b-752c-8efa-bcfaa9469928',
      valor: 'Nota Premiada',
    },
    { chave: 'e4fa9f5b-f6d8-4d45-a9ca-f186cfec717d', valor: 'Obras' },
    { chave: 'cee72cad-d38b-fbc3-916c-f1543d29ed9a', valor: 'Patrimônio' },
    { chave: '2e8f9372-7095-8009-e2d7-8089dff72695', valor: 'Planejamento' },
    {
      chave: '3b9e1f83-88e2-a4b4-63ae-718477db2c2f',
      valor: 'Prestação de Contas',
    },
    { chave: '1b780058-aaba-e466-e385-bb71f9d3cb0d', valor: 'Produto' },
    { chave: '91133697-ec08-62e0-fc31-e7c1fed25b79', valor: 'Tesouraria' },
    {
      chave: 'd68f5373-d6a4-65a6-9c2b-c1caa92b489f',
      valor: 'Transferências Governamentais',
    },
    { chave: '954a1310-456e-11ed-adc0-484d7efc03a3', valor: 'Transparência' },
    {
      chave: '22be1474-5845-4a2a-a17d-f171f49abec2',
      valor: 'Gestão de custos',
    },
  ]
  public iconsSystems: NebularSelect[] = [
    { texto: 'ação social', valor: 'acao social-8.png' },
    { texto: 'acesso nuvem', valor: 'acesso nuvem.png' },
    { texto: 'admissão', valor: 'admissão.png' },
    { texto: 'almoxarifado', valor: 'almoxarifado.png' },
    { texto: 'alvará', valor: 'alvará.png' },
    { texto: 'Ativo 18', valor: 'Ativo 18-8.png' },
    { texto: 'Ativo 25', valor: 'Ativo 25-8.png' },
    { texto: 'Atualização', valor: 'atualização.png' },
    { texto: 'avaliação', valor: 'avaliação.png' },
    { texto: 'B.I', valor: 'B.I.png' },
    { texto: 'Cadastros', valor: 'cadastros.png' },
    { texto: 'cemitério', valor: 'cemitério.png' },
    { texto: 'Central', valor: 'central.png' },
    { texto: 'chat', valor: 'chat.png' },
    { texto: 'Compras', valor: 'compras.png' },
    { texto: 'Contábil', valor: 'contabil.png' },
    { texto: 'Contábil', valor: 'Contábil.png' },
    { texto: 'Contrato', valor: 'contrato.png' },
    { texto: 'controle de frotas', valor: 'controle de frotas.png' },
    { texto: 'Controle interno', valor: 'controle-interno.png' },
    { texto: 'Custos', valor: 'Custos.png' },
    { texto: 'des if', valor: 'des if.png' },
    { texto: 'domicílio eletrônico', valor: 'domicílio eletrônico.png' },
    { texto: 'e156', valor: 'e156.png' },
    { texto: 'educa', valor: 'educa.png' },
    { texto: 'esocial', valor: 'esocial.png' },
    { texto: 'Execução orçamentaria', valor: 'execucao-orcamentaria.png' },
    { texto: 'Fiscalização', valor: 'Fiscalização.png' },
    { texto: 'folha de pagamento', valor: 'folha de pagamento.png' },
    { texto: 'gestão e tech', valor: 'gestão e tech.png' },
    { texto: 'Gestão de custos', valor: 'gestao-de-custos.png' },
    { texto: 'Gestão de recursos', valor: 'gestao-de-recursos.png' },
    { texto: 'infra nuvem', valor: 'infra nuvem.png' },
    { texto: 'infraestrutura de ti', valor: 'infraestrutura de ti.png' },
    { texto: 'isenção de iptu', valor: 'isenção de iptu.png' },
    { texto: 'Leis & atos', valor: 'leis-atos.png' },
    { texto: 'licitação e compras', valor: 'licitação e compras.png' },
    { texto: 'licitação', valor: 'licitacao.png' },
    { texto: 'nota fiscal eletrônica', valor: 'nota fiscal eletronica.png' },
    { texto: 'notificação', valor: 'notificação.png' },
    { texto: 'obras', valor: 'obras.png' },
    { texto: 'Orçamentário', valor: 'Orçamentario.png' },
    { texto: 'Patrimônio', valor: 'patrimonio.png' },
    { texto: 'Patrimônio', valor: 'Patrimônio.png' },
    { texto: 'ponto', valor: 'ponto.png' },
    { texto: 'Portal da transparência', valor: 'Portal da transparencia.png' },
    { texto: 'portal do contribuinte', valor: 'portal do contribuinte.png' },
    {
      texto: 'Portal da transparência',
      valor: 'portal-transparencia-white.png',
    },
    { texto: 'Prestação de contas', valor: 'prestacao-conta.png' },
    { texto: 'processos fiscais', valor: 'processos fiscais.png' },
    { texto: 'Produtos', valor: 'produto.png' },
    { texto: 'Rede SIm', valor: 'Rede SIm.png' },
    { texto: 'rh', valor: 'rh.png' },
    { texto: 'saúde e segurança', valor: 'saúde e segurança.png' },
    { texto: 'sem chat', valor: 'sem chat.png' },
    { texto: 'sem notificação', valor: 'sem notificação.png' },
    { texto: 'telefone', valor: 'telefone.png' },
    { texto: 'Tesouraria', valor: 'Tesouraria.png' },
    { texto: 'tramitação de processos', valor: 'tramitação de processos.png' },
    { texto: 'Transferência', valor: 'tranferencia.png' },
    { texto: 'Transferência', valor: 'transferencia.png' },
    { texto: 'Transparência', valor: 'transparencia.png' },
    { texto: 'tributário', valor: 'tributário.png' },
    { texto: 'webinar', valor: 'webinar.png' },
  ]

  constructor(
    private service: ModulesService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
  }

  private loadSelects(): void {}

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      modulo: this.formBuilder.group({
        uuid: [''],
        descricao: ['', [Validators.required, Validators.maxLength(100)]],
        icone: ['', [Validators.required, Validators.maxLength(100)]],
        link: ['', [Validators.required, Validators.maxLength(100)]],
        sistema: ['', [Validators.required, Validators.maxLength(100)]],
        status: ['', Validators.required],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) this.buscar(uuid)
      else this.loadSelects()
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('modulo').patchValue(data.dados)
        this.loadSelects()
      })
  }

  public cancelar(): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/controle-acesso/modulos`])
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.loading = true
        this.service
          .delete(this.formulario.get('modulo.uuid').value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Módulo excluído com sucesso.',
              })
              this.cancelar()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      this.loading = true
      if (this.formulario.get('modulo.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getModuloDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Módulo criado com sucesso.',
        })
        this.cancelar()
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getModuloDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Módulo atualizado com sucesso.',
        })
        this.cancelar()
      })
  }

  private getModuloDto(): any {
    const dto = this.formulario.getRawValue()

    return dto.modulo
  }
}
