::ng-deep #usersGrid {
  font-size: 12px;
}

::ng-deep #usersGrid .dx-datagrid-headers {
  font-weight: bold;
}

::ng-deep .dx-datagrid-group-opened::before {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  content: '\f07c';
}

::ng-deep .dx-datagrid-group-closed::before {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  content: '\f07b';
}

.drop{
  background-color: var(--input-basic-background-color);
  border: 1px solid var(--input-basic-border-color);
  box-sizing: border-box;
  box-shadow: none !important;
  border-radius: 0.25rem;
}

.revelar{
  position: absolute;
  cursor: pointer;
  text-align: left;
  top: 37px;
  right: 25px;
}