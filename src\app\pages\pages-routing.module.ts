import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'

import { DashboardComponent } from './dashboard/dashboard.component'
import { PagesComponent } from './pages.component'
import { NewsletterInfoComponent } from '@common/modules/newsletter-info/newsletter-info.component'
import { BoletimInformativoGuard } from '../auth/guards/boletim-informativo.guard'

const routes: Routes = [
  {
    path: '',
    component: PagesComponent,
    children: [
      { path: '', component: DashboardComponent },
      {
        path: 'administracao',
        loadChildren: () =>
          import('./administration/administration.module').then(
            m => m.AdministrationModule,
          ),
      },
      {
        path: 'equiplano',
        loadChildren: () =>
          import('./equiplano/equiplano.module').then(m => m.EquiplanoModule),
      },
      {
        path: 'usuario',
        loadChildren: () =>
          import('./user/user.module').then(m => m.UserModule),
      },
      {
        path: 'outros',
        loadChildren: () =>
          import('./miscellaneous/miscellaneous.module').then(
            m => m.MiscellaneousModule,
          ),
      },
      {
        path: 'auditoria',
        loadChildren: () =>
          import('./audit/audit.module').then(m => m.AuditModule),
      },
      {
        path: 'boletim-informativo',
        canActivate: [BoletimInformativoGuard],
        loadChildren: () =>
          import('./newsletter/newsletter.module').then(
            m => m.NewsletterModule,
          ),
      }, 
      {
        path: 'boletim-informativo-visualizar',
        component: NewsletterInfoComponent
      },
      { path: '**', redirectTo: 'outros', pathMatch: 'full' },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PagesRoutingModule {}
