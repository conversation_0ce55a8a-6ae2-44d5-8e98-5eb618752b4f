{"name": "eqp-central", "version": "1.0.20", "license": "MIT", "scripts": {"ng": "ng", "conventional-changelog": "conventional-changelog", "start": "ng serve", "start:proxy": "ng serve --proxy-config proxy.config.js", "build:master": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --configuration=production", "build:dev": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --configuration=dev", "build:homolog": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --configuration=homolog", "build:apresentacao": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --configuration=apresentacao", "build:treinamento": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --configuration=treinamento", "build:prod": "npm run build -- --prod --aot", "test": "ng test", "test:coverage": "rimraf coverage && npm run test -- --code-coverage", "lint": "ng lint", "lint:fix": "ng lint eqp-central --fix", "lint:styles": "stylelint ./src/**/*.scss", "lint:ci": "npm run lint && npm run lint:styles", "pree2e": "webdriver-manager update --standalone false --gecko false", "e2e": "ng e2e", "docs": "compodoc -p tsconfig.json -d docs -w -s", "docs:serve": "compodoc -p tsconfig.json -d docs -s", "release:changelog": "npm run conventional-changelog -- -p angular -i CHANGELOG.md -s", "postinstall": "ngcc --properties es2015 es5 browser module main --first-only --create-ivy-entry-points --tsconfig \"./src/tsconfig.app.json\""}, "dependencies": {"@angular/animations": "^11.0.9", "@angular/cdk": "^11.2.8", "@angular/common": "^11.0.9", "@angular/compiler": "^11.0.9", "@angular/core": "^11.0.9", "@angular/forms": "^11.0.9", "@angular/material": "^11.2.8", "@angular/platform-browser": "^11.0.9", "@angular/platform-browser-dynamic": "^11.0.9", "@angular/router": "^11.0.9", "@angular/service-worker": "^11.0.9", "@elastic/apm-rum-angular": "^2.0.1", "@nebular/auth": "7.0.0", "@nebular/eva-icons": "7.0.0", "@nebular/moment": "^7.0.0", "@nebular/security": "7.0.0", "@nebular/theme": "7.0.0", "angular2-text-mask": "^9.0.0", "bootstrap": "4.3.1", "classlist.js": "1.1.20150312", "core-js": "2.5.1", "cropperjs": "^1.5.12", "cross-storage": "^1.0.0", "crypto-js": "^4.2.0", "deep-cleaner": "^1.2.1", "devextreme": "21.2.6", "devextreme-angular": "21.2.6", "devextreme-aspnet-data-nojquery": "^2.8.3", "eva-icons": "^1.1.3", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "intl": "1.2.5", "ionicons": "2.0.1", "jspdf": "^3.0.1", "nebular-icons": "1.1.0", "ngx-currency": "^2.2.2", "ngx-image-cropper": "^3.3.5", "ngx-mask": "^8.1.7", "normalize.css": "6.0.0", "pace-js": "1.0.2", "roboto-fontface": "0.8.0", "rupture-sass": "^0.3.0", "rxjs": "6.6.2", "rxjs-compat": "6.3.0", "socicon": "3.0.5", "style-loader": "^1.1.3", "text-mask-addons": "^3.8.0", "tinymce": "4.5.7", "tslib": "^2.0.0", "typeface-exo": "0.0.22", "web-animations-js": "^2.3.2", "zone.js": "~0.10.3"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1100.7", "@angular/cli": "^11.0.7", "@angular/compiler-cli": "^11.0.9", "@angular/language-service": "11.0.9", "@compodoc/compodoc": "^1.1.11", "@fortawesome/fontawesome-free": "^6.4.2", "@types/d3-color": "1.0.5", "@types/file-saver": "^2.0.7", "@types/jasmine": "^3.6.11", "@types/jasminewd2": "^2.0.8", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "conventional-changelog-cli": "1.3.4", "husky": "0.13.3", "jasmine-core": "^3.7.1", "jasmine-spec-reporter": "^7.0.0", "karma": "~5.1.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "moment-es6": "^1.0.0", "npm-run-all": "4.0.2", "protractor": "~7.0.0", "rimraf": "2.6.1", "stylelint": "7.13.0", "ts-node": "3.2.2", "tslint": "6.1.3", "tslint-language-service": "^0.9.9", "typescript": "4.0.5"}}