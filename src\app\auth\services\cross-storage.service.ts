import { Injectable } from '@angular/core'
import { CrossStorageHub } from 'cross-storage'

import { AllowedOrigins } from '../helpers/allowed-origins'
import { Origin } from '../interfaces/origin'

@Injectable({
  providedIn: 'root',
})
export class CrossStorageService {
  private _wayBack: string
  public get wayBack(): string {
    return this._wayBack
  }
  public set wayBack(value: string) {
    this._wayBack = value
  }

  private origins: Origin[] = AllowedOrigins

  constructor() {}

  public registerHub(): void {
    CrossStorageHub.init([...this.origins])
  }
}
