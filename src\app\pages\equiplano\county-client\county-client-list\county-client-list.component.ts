import { Component, OnD<PERSON>roy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import {
  ModalConfirmarComponent,
} from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'

import {
  ToastrService,
} from './../../../../@common/services/toastr/toastr.service'
import { CountyClientService } from './../county-client.service'

@Component({
  selector: 'eqp-county-client-list',
  templateUrl: './county-client-list.component.html',
  styleUrls: ['./county-client-list.component.scss'],
})
export class CountyClientListComponent
  extends BaseTelasComponent
  implements OnInit, OnD<PERSON>roy
{
  public loading: boolean = false
  public pageTitle: string = 'Equiplano | Cliente/ Município'

  public gridData: any

  private subscription: Subscription
  constructor(
    private service: CountyClientService,
    private toastr: ToastrService,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
  }

  public ngOnInit(): void {
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      'municipio_cliente',
      10,
    )
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items[0].showText = 'ever'
    event.toolbarOptions.items[0].options.text = 'Novo Cliente/ Município'
    event.toolbarOptions.items[0].options.hint = 'Novo Cliente/ Município'

    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/county-client/novo`])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/county-client/edit/${uuid}`])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Município excluído com sucesso.',
              })
              this.gridData = this.service.getDataSourceFiltro(
                'uuid',
                'municipio_cliente',
                10,
              )
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
