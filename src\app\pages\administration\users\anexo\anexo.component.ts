import { Component, OnInit, ViewChild } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { FileParser } from '@design-tools/file-handling/file-parser'
import { NbDialogRef } from '@nebular/theme'

@Component({
  selector: 'eqp-anexo',
  templateUrl: './anexo.component.html',
  styleUrls: ['./anexo.component.scss'],
})
export class AnexoComponent implements OnInit {
  public formulario: FormGroup

  @ViewChild('file', { static: false }) public file: any
  public maxSize: string
  public files: Set<File> = new Set()

  constructor(
    protected ref: NbDialogRef<AnexoComponent>,
    private formBuilder: FormBuilder,
    private toastr: ToastrService,
    private fileParser: FileParser,
  ) {}

  ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      uuid: [null],
      nome: ['ATTACHMENT'],
      arquivo: [null],
      descricao: [null, Validators.required],
      link: [null],
    })
  }

  public dismiss(): void {
    this.ref.close(null)
  }

  public adicionar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Preencher os campos obrigatórios.',
      })
      this.formulario.markAllAsTouched()
    } else {
      const anexo = this.formulario.getRawValue()

      this.ref.close(anexo)
    }
  }

  public uploadFile(event: any): void {
    const files: { [key: string]: File } = this.file.nativeElement.files
    if (files.length) {
      for (const key in files) {
        if (!isNaN(parseInt(key, 10))) {
          if (files[key].size > parseInt(this.maxSize, 10) * 1000000) {
            this.toastr.send({
              error: true,
              // tslint:disable-next-line: max-line-length
              message:
                'O tamanho do arquivo excede o máximo permitido de ' +
                this.maxSize +
                'MB',
            })

            return
          }

          if (
            files[key].type === 'application/sql' ||
            files[key].name.indexOf('.sql') > -1
          ) {
            this.toastr.send({
              error: true,
              message: 'Formato de arquivo não permitido',
            })

            return
          }
          if (
            files[key].type === 'application/x-msdownload' ||
            files[key].name.indexOf('.exe') > -1
          ) {
            this.toastr.send({
              error: true,
              message: 'Formato de arquivo não permitido',
            })

            return
          }
          this.files.add(files[key])
        }
      }

      this.formulario.get('descricao').patchValue(event.target.files[0].name)
      this.formulario.get('link').patchValue('')

      let me = this
      const reader = new FileReader()

      reader.readAsDataURL(files[0])
      reader.onload = () => {
        const file = {
          link: reader.result.toString(),
          nome: files[0].name,
          tamanho: (files[0].size / 1000000).toString(),
          tipo_arquivo: files[0].type,
          tipo: me.fileParser.getFileType(files[0]),
          publicaInternet: 'YES',
        }

        if (file) {
          this.formulario.get('arquivo').patchValue(file.link)
        }
      }
      reader.onerror = function (error) {}
    }
  }
}
