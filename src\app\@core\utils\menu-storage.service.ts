import { Injectable, OnDestroy } from '@angular/core';
import { EqpNbMenuItem } from '@common/interfaces/dtos/menu';
import { Store } from '@common/services/store/store';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class MenuStorageService implements OnDestroy {
  private _unsubscriber$: Subject<void> = new Subject<void>();

  private _store = new Store<{
    loading: boolean;
    singleMenu: EqpNbMenuItem[];
    allMenu: EqpNbMenuItem[];
  }>(
    {
      singleMenu: [],
      allMenu: [],
      loading: true,
    },
    this._unsubscriber$,
  );

  ngOnDestroy(): void {
    this._unsubscriber$.next();
    this._unsubscriber$.complete();
  }

  public get store() {
    return this._store;
  }

  public setBreadcrumbData(allMenu: EqpNbMenuItem[]) {
    this._store.setState({
      allMenu,
      singleMenu: this._flattenTree(allMenu),
      loading: false,
    });
  }

  private _flattenTree(lista: EqpNbMenuItem[]): EqpNbMenuItem[] {
    const resultado: EqpNbMenuItem[] = [];

    const traverse = (itens: EqpNbMenuItem[]) => {
      for (const item of itens) {
        const copia = { ...item };
        delete copia.children;
        resultado.push(copia);

        if (item.children && Array.isArray(item.children)) {
          traverse(item.children);
        }
      }
    };

    traverse(lista);
    return resultado;
  }

  private _setUUID(obj: any): void {
    obj.uuid = this._generateUUID();
  }

  private _generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  public copyListWithUUID(lista: any[]): any[] {
    return lista.map(item => {
      const novoItem = { ...item };

      this._setUUID(novoItem);

      if (item.children && Array.isArray(item.children)) {
        novoItem.children = this.copyListWithUUID(item.children);
      }
      return novoItem;
    });
  }
}
