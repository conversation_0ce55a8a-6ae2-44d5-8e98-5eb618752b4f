import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { ApiResponse } from '@core/data/api-response'
import { ResponseDto } from '@core/data/response-dto'
import { SistemaModuloNome } from '@pages/newsletter/modules-enum'
import { NewsletterInterface } from '@pages/newsletter/newsletter'
import { Subject } from 'rxjs'

interface BoletimResponse {
  data: NewsletterInterface[]
  totalCount: number
}

@Injectable({
  providedIn: 'root',
})
export class NewsletterInfoService {
  private readonly MODULE_KEY = 'BACK_OFFICE'
  public accordionExpanded$ = new Subject<boolean>()

  constructor(private _http: HttpClient) {}

  public loadModules() {
    const headers = new HttpHeaders()
    return this._http.get<ApiResponse<any>>(
      'boletim-informativo/modulos?take=0',
      {
        headers,
      },
    )
  }

  // mostra a quantidade de boletins não lidos para o módulo logado
  public loadTotalNewsletters() {
    const headers = new HttpHeaders()
    return this._http.get<ResponseDto<number>>(
      `boletim-informativo/notificacao/${this.MODULE_KEY}`,
      {
        headers,
      },
    )
  }

  public getAll() {
    const headers = new HttpHeaders()
    return this._http.get<ApiResponse<NewsletterInterface>>(
      'boletim-informativo?take=0',
      {
        headers,
      },
    )
  }

  public getAllByModule(dataParams: {
    moduloUuid: string
    skip: number
    take: number
    termoPesquisa?: string
  }) {
    const { moduloUuid, skip, take, termoPesquisa } = dataParams
    const headers = new HttpHeaders()

    let filter = [
      ['moduloUuid', '=', moduloUuid],
      'and',
      ['publicar', '=', 'S'],
    ]
    const sort = [{ selector: 'data', desc: true }]

    if (termoPesquisa && termoPesquisa.trim() !== '') {
      const pesquisaFilter = [
        ['titulo', 'contains', termoPesquisa],
        'or',
        ['descricao', 'contains', termoPesquisa],
      ]
      filter = filter.concat(['and', ...pesquisaFilter])
    }

    let params = new HttpParams().append('filter', JSON.stringify(filter))
    params = params.append('skip', skip.toString())
    params = params.append('take', take.toString())
    params = params.append('sort', JSON.stringify(sort))

    return this._http.get<BoletimResponse>('boletim-informativo', {
      headers,
      params,
    })
  }

  public listaPublicacao() {
    const headers = new HttpHeaders()

    return this._http.get<ApiResponse<any>>(
      'boletim-informativo/lista-usuario',
      {
        headers,
      },
    )
  }

  obterNomeModulo(): string {
    return SistemaModuloNome[this.MODULE_KEY]
  }

  public getByUiid(uuid: string) {
    const headers = new HttpHeaders()

    return this._http.get<ResponseDto<NewsletterInterface>>(
      `boletim-informativo/${uuid}`,
      {
        headers,
      },
    )
  }
}
