{"items": [{"key": "$base-bg", "value": "rgba(34, 43, 69, 1)"}, {"key": "$base-success", "value": "rgba(0, 214, 143, 1)"}, {"key": "$base-warning", "value": "rgba(255, 170, 0, 1)"}, {"key": "$base-danger", "value": "rgba(255, 61, 113, 1)"}, {"key": "$toast-info-bg", "value": "rgba(0, 149, 255, 1)"}, {"key": "$base-accent", "value": "rgba(51, 102, 255, 1)"}, {"key": "$base-border-color", "value": "rgba(16, 20, 38, 1)"}, {"key": "$button-default-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$button-success-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$button-danger-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$datagrid-selection-bg", "value": "rgba(51, 102, 255, 0.23)"}, {"key": "$texteditor-bg", "value": "rgba(255, 255, 255, 0)"}, {"key": "$texteditor-hover-bg", "value": "rgba(255, 255, 255, 0)"}, {"key": "$datagrid-menu-icon-color", "value": "rgba(51, 102, 255, 1)"}, {"key": "$datagrid-row-focused-color", "value": "rgba(255, 255, 255, 0.87)"}, {"key": "$datagrid-row-error-bg", "value": "rgba(255, 139, 170, 1)"}, {"key": "$scheduler-appointment-text-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$texteditor-border-color", "value": "rgba(16, 20, 38, 0.7)"}, {"key": "$texteditor-placeholder-color", "value": "rgba(166, 166, 166, 0.79)"}, {"key": "$texteditor-hover-border-color", "value": "rgba(57, 71, 134, 1)"}, {"key": "$tagbox-tag-button-remove-bg", "value": "rgba(0, 0, 0, 0.36)"}, {"key": "$tagbox-tag-bg", "value": "rgba(224, 224, 224, 1)"}, {"key": "$tagbox-tag-color", "value": "rgba(0, 0, 0, 0.6)"}, {"key": "$tagbox-tag-active-color", "value": "rgba(0, 0, 0, 0.87)"}, {"key": "$tooltip-bg", "value": "rgba(51, 102, 255, 1)"}, {"key": "$material-slider-tooltip-color", "value": "rgba(255, 255, 255, 1)"}, {"key": "$accordion-title-color", "value": "rgba(51, 102, 255, 1)"}, {"key": "$drawer-shader-background-color", "value": "rgba(51, 102, 255, 0.5)"}, {"key": "$scrollable-scroll-bg", "value": "rgba(51, 102, 255, 0.7)"}], "baseTheme": "material.blue.dark.compact", "outputColorScheme": "nebular-dark", "makeSwatch": false, "version": "21.2.6", "widgets": [], "removeExternalResources": false}