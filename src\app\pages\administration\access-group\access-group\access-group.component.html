<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
    <div class="mt-2">
      <eqp-breadcrumb></eqp-breadcrumb>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="accessGroup">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="nome"
          name="Nome"
          label="Nome"
          placeholder="Nome"
          required="true"
          errorMessage="É obrigatório preencher o nome"
          maxlength="250"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'basic'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="descricao"
          name="Descrição"
          label="Descrição"
          placeholder="Descrição"
          required="true"
          errorMessage="É obrigatório preencher a descrição"
          maxlength="250"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Cliente"
          label="Cliente"
          placeholder="Cliente"
          formControlName="clienteUuid"
          [dataSource]="clientesData"
          valueExpr="uuid"
          displayExpr="nome"
          required="true"
        ></eqp-nebular-select>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Módulo"
          label="Módulo"
          placeholder="Módulo"
          formControlName="moduloUuid"
          [dataSource]="moduloData"
          valueExpr="uuid"
          displayExpr="descricao"
          required="true"
        ></eqp-nebular-select>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Nível de Acesso"
          label="Nível de Acesso"
          placeholder="Nível de Acesso"
          formControlName="nivelAcesso"
          [dataSource]="nivelAcessoData"
          valueExpr="chave"
          displayExpr="valor"
          required="true"
        ></eqp-nebular-select>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Status"
          label="Status"
          placeholder="Status"
          formControlName="status"
          [dataSource]="statuses"
          valueExpr="valor"
          displayExpr="texto"
          required="true"
        ></eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="
            formulario.get('accessGroup.uuid').value &&
            nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          type="button"
          *ngIf="
            (formulario.get('accessGroup.uuid').value &&
              nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
