import { Component, Input, OnInit } from '@angular/core'
import { ToastrService } from '@common/services/toastr/toastr.service'

import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { NbDialogService } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { first } from 'rxjs/operators'
import { PermissaoListaComponent } from '../permissao-lista/permissao-lista.component'
import { AccessGroupService } from './../access-group.service'

@Component({
  selector: 'eqp-permissions',
  templateUrl: './permissions.component.html',
  styleUrls: ['./permissions.component.scss'],
})
export class PermissionsComponent implements OnInit {
  @Input()
  public grupoAcesso: any

  @Input()
  public nivelPermissao: string

  public gridData: any
  public loading: boolean = false
  public nivelAcessoData: any

  constructor(
    private service: AccessGroupService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {}

  public ngOnInit(): void {
    this.nivelAcessoData = {
      store: [
        { nome: 'Visualizador', valor: 'VIEWER' },
        { nome: 'Editor', valor: 'EDITOR' },
        { nome: 'Todos os Acessos', valor: 'FULL' },
      ],
      paginate: true,
      pageSize: 10,
    }
    this.fetchGrid()
  }

  private fetchGrid(): void {
    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltro(
        'uuid',
        `grupo_acesso/${this.grupoAcesso.uuid}/permissao`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.name === 'addRowButton') {
        item.showText = 'ever'
        item.options.text = 'Nova permissão'
        item.options.hint = 'Nova permissão'
        item.options.onClick = () => this.novoRegistro()
      }

      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    const dialogRef = this.dialogService.open(PermissaoListaComponent, {
      context: {
        grupoAcesso: this.grupoAcesso,
      },
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno.acoesUuid && retorno.acoesUuid.length > 0) {
        this.service
          .postPermissao(retorno)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Permissão incluida com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .deletePermissao(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Permissão excluída com sucesso.',
              })
              this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public mostrarNivel(data): string {
    if (data.data.nivelAcesso === 'VIEWER') return 'Visualizador'
    else if (data.data.nivelAcesso === 'EDITOR') return 'Editor'
    else return 'Todos os Acessos'
  }
}
