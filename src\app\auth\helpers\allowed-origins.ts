import { Origin } from '../interfaces/origin'

export const AllowedOrigins: Origin[] = [
  {
    origin: /localhost:4200$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4202$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4203$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4204$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4205$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4207$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4208$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4209$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4210$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /localhost:4211$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /cadastros-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /cadastros-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /cadastros.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /cadastros-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /cadastros-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /compra-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /compra-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /compra.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /compra-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /compra-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /leis-atos-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /leis-atos-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /leis-atos.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /leis-atos-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /leis-atos-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /prestacao-contas-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /prestacao-contas-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /prestacao-contas.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /prestacao-contas-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /prestacao-contas-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /planejamento-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /planejamento-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /planejamento.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /planejamento-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /planejamento-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /produto-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /produto-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /produto.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /produto-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /produto-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /formacao-preco-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /formacao-preco-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /formacao-preco.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /formacao-preco-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /formacao-preco-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /licitacao-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /licitacao-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /licitacao.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /licitacao-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /licitacao-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /transferencias-gov-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /transferencias-gov-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /transferencias-gov.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /transferencias-gov-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /transferencias-gov-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /tesouraria-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /tesouraria-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /tesouraria.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /tesouraria-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /tesouraria-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contrato-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contrato-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contrato.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contrato-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contrato-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contabil-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contabil-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contabil.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contabil-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /contabil-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /execucao-orcamentaria-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /execucao-orcamentaria-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /execucao-orcamentaria.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /execucao-orcamentaria-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /execucao-orcamentaria-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /obras-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /obras-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /obras.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /obras-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /obras-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /patrimonio-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /patrimonio-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /patrimonio.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /patrimonio-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /patrimonio-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-transparencia-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-transparencia-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-transparencia.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-transparencia-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-transparencia-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-interno-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-interno-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-interno.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-interno-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-interno-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /gestao-custos-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /gestao-custos-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /gestao-custos.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /gestao-custos-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /gestao-custos-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-dotacao-front-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-dotacao-front-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-dotacao.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-dotacao-front-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /controle-dotacao-front-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-nota-premiada-dev.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-nota-premiada-homolog.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-nota-premiada.equiplano.cloud$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-nota-premiada-apresentacao.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
  {
    origin: /painel-nota-premiada-treinamento.ops.equiplano.com.br$/,
    allow: ['get', 'set', 'del', 'getKeys', 'clear'],
  },
]
