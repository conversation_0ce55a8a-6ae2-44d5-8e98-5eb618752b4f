import { Component, OnInit, ViewChild } from '@angular/core'

import { ParametersComponent } from './../parameters/parameters.component'

@Component({
  selector: 'eqp-parameters-list',
  templateUrl: './parameters-list.component.html',
  styleUrls: ['./parameters-list.component.scss'],
})
export class ParametersListComponent implements OnInit {
  public pageTitle: string = 'Controle de acesso | Parâmetros'

  @ViewChild('parameters', { static: true }) parameters: ParametersComponent

  constructor() {}

  public ngOnInit(): void {}

  public fetchGrid(): void {
    this.parameters.fetchGrid()
  }
}
