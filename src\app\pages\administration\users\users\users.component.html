<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
    <div class="mt-2">
      <eqp-breadcrumb></eqp-breadcrumb>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <nb-tabset>
      <nb-tab tabTitle="Usuário">
        <div class="row" formGroupName="users">
          <div class="col-md-4 col-sm-12 mb-4" *ngIf="adminEqp === 'S'">
            <eqp-field-toggle
              class="toggle-label"
              label="Administrador Equiplano"
              labelPosition="right"
              formControlName="adminEquiplano"
            ></eqp-field-toggle>
          </div>
          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-field-toggle
              class="toggle-label"
              label="Limitação de Acesso"
              labelPosition="right"
              formControlName="limitacaoAcesso"
            ></eqp-field-toggle>
          </div>
          <div class="col-md-4 col-sm-12 mb-4" *ngIf="adminEqp === 'S'">
            <eqp-field-toggle
              class="toggle-label"
              label="Senha expira"
              labelPosition="right"
              formControlName="senhaExpira"
            ></eqp-field-toggle>
          </div>
          <div class="col-md-8 col-sm-12 mb-8" *ngIf="adminEqp === 'N'">
            <eqp-field-toggle
              class="toggle-label"
              label="Senha expira"
              labelPosition="right"
              formControlName="senhaExpira"
            ></eqp-field-toggle>
          </div>

          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              primaryMask="000.000.000-00"
              formControlName="cpf"
              name="CPF"
              label="CPF"
              placeholder="CPF"
              required="true"
              errorMessage="É obrigatório preencher o CPF"
              [disabled]="formulario.get('users.uuid').value ? true : false"
            >
            </eqp-nebular-input>
          </div>

          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="nome"
              name="Nome"
              label="Nome"
              placeholder="Nome"
              required="true"
              errorMessage="É obrigatório preencher o Nome"
              maxlength="100"
            >
            </eqp-nebular-input>
          </div>

          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="email"
              name="E-mail"
              label="E-mail"
              placeholder="E-mail"
              required="true"
              maxlength="100"
              errorMessage="É obrigatório preencher o E-mail"
            >
            </eqp-nebular-input>
          </div>

          <div
            class="col-md-4 col-sm-12 mb-4"
            *ngIf="!formulario.get('users.uuid').value"
          >
            <eqp-nebular-input
              [style]="'basic'"
              [size]="'small'"
              [shape]="'rectangle'"
              [type]="typeText"
              formControlName="senha"
              name="Senha"
              label="Senha"
              placeholder="Senha"
              errorMessage="É obrigatório preencher a Senha"
              [required]="senhaObrigatoria"
              (blur)="mensagemErroSenha()"
              maxlength="100"
            >
            </eqp-nebular-input>
          </div>

          <div class="col-md-4 col-sm-12 mb-4">
            <eqp-nebular-select
              [size]="'small'"
              [shape]="'rectangle'"
              name="Status"
              label="Status"
              placeholder="Status"
              formControlName="status"
              [dataSource]="statuses"
              valueExpr="valor"
              displayExpr="texto"
              required="true"
            ></eqp-nebular-select>
          </div>
          <div
            class="col-md-4 col-sm-12 mb-4"
            *ngIf="
              !formulario.get('users.uuid').value &&
              formulario.get('users.adminEquiplano').value === 'N'
            "
          >
            <label class="label"> Grupo de acesso*</label>
            <dx-drop-down-box
              class="drop"
              placeholder="Grupo de acesso"
              [(value)]="gridBoxValue"
              [dataSource]="gruposAcesso"
              valueExpr="uuid"
              displayExpr="nome"
              required="!formulario.get('users.uuid').value"
              stylingMode="outlined"
              [showClearButton]="true"
              stylingMode="outlined"
            >
              <div *dxTemplate="let data of 'content'">
                <dx-data-grid
                  id="accessGroupGrid"
                  [dataSource]="gruposAcesso"
                  [selection]="{ mode: 'multiple' }"
                  [hoverStateEnabled]="true"
                  [paging]="{ enabled: true, pageSize: 10 }"
                  [filterRow]="{ visible: true }"
                  [scrolling]="{ mode: 'virtual' }"
                  [(selectedRowKeys)]="gridBoxValue"
                  [showColumnLines]="false"
                  [showRowLines]="false"
                  [showBorders]="false"
                  [rowAlternationEnabled]="true"
                  [wordWrapEnabled]="true"
                  keyExpr="uuid"
                  [height]="345"
                >
                  <dxo-grouping #expand [autoExpandAll]="false"></dxo-grouping>
                  <dxi-column
                    dataField="uuid"
                    caption=""
                    [visible]="false"
                  ></dxi-column>

                  <dxi-column dataField="nome" caption="Nome"></dxi-column>
                  <dxi-column
                    dataField="clienteNome"
                    caption="Entidade"
                    [groupIndex]="0"
                  ></dxi-column>
                </dx-data-grid>
              </div>
            </dx-drop-down-box>
          </div>
          <div
            class="col-md-4 col-sm-12 mb-4"
            *ngIf="formulario.get('users.senhaExpira').value === 'S'"
          >
            <eqp-nebular-input
              [style]="'date'"
              [size]="'small'"
              [shape]="'rectangle'"
              formControlName="dataExpiracaoSenha"
              name="Data de expiração"
              label="Data de expiração"
              placeholder="Data de expiração"
            >
            </eqp-nebular-input>
          </div>
        </div>
      </nb-tab>
      <nb-tab tabTitle="Anexos">
        <dx-data-grid
          id="anexoGrid"
          #anexoGrid
          [dataSource]="gridData"
          [allowColumnResizing]="true"
          [columnAutoWidth]="true"
          [showColumnLines]="false"
          [showRowLines]="false"
          [showBorders]="false"
          [rowAlternationEnabled]="true"
          [wordWrapEnabled]="true"
          [loadPanel]="false"
          keyExpr="uuid"
          (onToolbarPreparing)="onToolbarPreparing($event)"
          (onInitNewRow)="novoRegistro()"
        >
          <dxo-export
            [enabled]="true"
            [excelWrapTextEnabled]="true"
            [excelFilterEnabled]="true"
            fileName="Anexo"
          ></dxo-export>

          <dxo-paging [pageSize]="10"></dxo-paging>
          <dxo-pager
            [showInfo]="true"
            [showNavigationButtons]="true"
            [showPageSizeSelector]="false"
          >
          </dxo-pager>

          <dxo-header-filter [visible]="false"> </dxo-header-filter>
          <dxo-filter-row [visible]="true"></dxo-filter-row>

          <dxo-sorting mode="multiple"></dxo-sorting>

          <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

          <dxo-group-panel
            [visible]="false"
            [emptyPanelText]="''"
          ></dxo-group-panel>

          <dxo-search-panel
            [visible]="true"
            placeholder="Buscar anexo"
          ></dxo-search-panel>

          <dxo-editing
            mode="form"
            [allowUpdating]="false"
            [allowDeleting]="false"
            [allowAdding]="
              nivelPermissao === 'FULL' || nivelPermissao === 'EDITOR'
            "
            [useIcons]="true"
          >
          </dxo-editing>

          <dxi-column
            dataField="nome"
            caption="Nome"
            [visible]="false"
          ></dxi-column>
          <dxi-column
            dataField="arquivo"
            caption="Arquivo"
            [visible]="false"
          ></dxi-column>
          <dxi-column dataField="descricao" caption="Descrição"></dxi-column>
          <dxi-column
            dataField="link"
            caption="Link"
            [visible]="false"
          ></dxi-column>

          <dxi-column
            dataField="uuid"
            caption=""
            [width]="80"
            [allowFiltering]="false"
            [allowSorting]="false"
            cellTemplate="acaoColumn"
          ></dxi-column>

          <div *dxTemplate="let data of 'acaoColumn'">
            <a
              (click)="abrir(data)"
              class="dx-link dx-link-edit fas fa-search dx-link-icon btn-icon-grid"
            >
            </a>
            <a
              *ngIf="nivelPermissao === 'FULL'"
              (click)="removerAnexo(data)"
              class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
            >
            </a>
          </div>
        </dx-data-grid>
      </nb-tab>
    </nb-tabset>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="
            formulario.get('users.uuid').value && nivelPermissao === 'FULL'
          "
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          type="button"
          *ngIf="
            (formulario.get('users.uuid').value &&
              nivelPermissao === 'EDITOR') ||
            nivelPermissao === 'FULL'
          "
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
