import {
  Component,
  forwardRef,
  Host,
  Input,
  OnInit,
  Optional,
  SkipSelf,
} from '@angular/core'
import {
  AbstractControl,
  ControlContainer,
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
} from '@angular/forms'
import { FileDto } from '@interfaces/dtos/file-dto'
import { NbDialogService } from '@nebular/theme'

import { ChooseFilesComponent } from '../choose-files/choose-files.component'
import { FileParser } from '../file-parser'

const valueProvider = {
  provide: NG_VALUE_ACCESSOR,
  // tslint:disable-next-line:no-use-before-declare
  useExisting: forwardRef(() => FileInputComponent),
  multi: true,
}

enum Paths {
  TEXT = 'assets/img/file-txt.png',
  DOC = 'assets/img/file-doc.png',
  GIF = 'assets/img/file-gif.png',
  HTML = 'assets/img/file-html.png',
  JPG = 'assets/img/file-jpg.png',
  MP3 = 'assets/img/file-mp3.png',
  MPG = 'assets/img/file-mpg.png',
  PDF = 'assets/img/file-pdf.png',
  PNG = 'assets/img/file-png.png',
  PPT = 'assets/img/file-ppt.png',
  XLS = 'assets/img/file-xls.png',
  XML = 'assets/img/file-xml.png',
  ZIP = 'assets/img/file-zip.png',
  NONE = 'assets/img/file-none.png',
}

@Component({
  selector: 'eqp-file-input',
  templateUrl: './file-input.component.html',
  styleUrls: ['./file-input.component.scss'],
  providers: [valueProvider],
})
export class FileInputComponent implements ControlValueAccessor, OnInit {
  @Input() public label?: string
  @Input() public readonly?: boolean = false
  @Input() public required?: boolean = false
  @Input() public class?: string
  @Input() public errorMessage?: string = 'Favor preencher o campo'
  @Input() public dialogTitle?: string = 'Gerenciador de arquivos | Anexo'
  @Input() private formControlName: string
  @Input() public value?: FileDto[]
  @Input() public maxSize: string
  @Input() public hideLabel: boolean = false
  @Input() public description: boolean = false

  @Input()
  public set initialValue(files: FileDto[]) {
    this.files = files
    this.value = this.files

    this.propagateChange(this.files)
    this.propagateChange(this.value)
  }

  public files: FileDto[] = []
  public control: AbstractControl
  public path: typeof Paths = Paths

  @Input() private baseUrl: string = ''
  @Input() public inputClick?: any = () => true

  private propagateChange: any = () => {}

  constructor(
    @Optional()
    @Host()
    @SkipSelf()
    private controlContainer: ControlContainer,
    private dialogService: NbDialogService,
    private fileParser: FileParser,
  ) {}

  public ngOnInit(): void {
    if (this.controlContainer && this.controlContainer.control) {
      this.control = this.controlContainer.control.get(this.formControlName)
    }
  }

  public registerOnTouched(fn: () => void): void {}

  public writeValue(value: any): void {
    if (value) {
      this.value = value
    } else {
      this.value = this.files = []
    }
  }

  public registerOnChange(fn: any): void {
    this.propagateChange = fn
  }

  public modelChanged(event: any): void {
    this.propagateChange(event)
  }

  public getFileSrc(file: any): string {
    const name = file.name ? file.name : file.nome

    return this.path[this.fileParser.getFileTypeByFileExtension(name)]
  }

  public removeFile(index: number): void {
    this.files.splice(index, 1)

    this.getAbsControl().setValue(this.files)
  }

  public openFileUploadModal(): void {
    const dialogRef = this.dialogService.open(ChooseFilesComponent, {})

    dialogRef.componentRef.instance.dialogTitle = this.dialogTitle
    dialogRef.componentRef.instance.description = this.description
    dialogRef.componentRef.instance.maxSize = this.maxSize

    dialogRef.onClose.subscribe(data => {
      if (data) {
        this.files.push(data)
        this.getAbsControl().setValue(this.files)
      } else null
    })
  }

  private getAbsControl(): any {
    return this.controlContainer.control.get(this.formControlName)
  }
}
