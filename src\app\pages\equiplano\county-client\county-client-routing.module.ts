import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'

import { CountyClientListComponent } from './county-client-list/county-client-list.component'
import { CountyClientComponent } from './county-client/county-client.component'

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: '',
        component: CountyClientListComponent,
      },
      {
        path: 'novo',
        component: CountyClientComponent,
      },
      {
        path: 'edit/:uuid',
        component: CountyClientComponent,
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CountyClientRoutingModule {}
