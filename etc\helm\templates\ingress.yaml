{{- $fullName := include "application.fullname" . -}}
{{- $servicePort  := .Values.service.port -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  {{- with .Values.ingress.annotations }}
  annotations:
    nginx.ingress.kubernetes.io/configuration-snippet: |
      if ($http_x_forwarded_proto = "http") {
       return 301 https://$host$request_uri;
      }
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
  name: {{ $fullName }}
spec:
  rules:
  {{- range .Values.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
        {{- range .paths }}
          - path: {{ . }}
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $servicePort }}
        {{- end }}
  {{- end }}
