<nb-card>
    <nb-card-header>
      <div class="row">
        <div class="col-md-8">
          <h5>{{ pageTitle }}</h5>
        </div>
      </div>
    <div class="mt-2">
      <eqp-breadcrumb></eqp-breadcrumb>
    </div>
    </nb-card-header>
        <nb-card-body>
            <dx-data-grid
            id="configEmailGrid"
            [dataSource]="gridData"
            [allowColumnResizing]="true"
            [columnAutoWidth]="true"
            [showColumnLines]="false"
            [showRowLines]="false"
            [showBorders]="false"
            [rowAlternationEnabled]="true"
            [wordWrapEnabled]="true"
            [loadPanel]="false"
            [columnHidingEnabled]="true"
            [remoteOperations]="true"
            keyExpr="uuid"
            (onToolbarPreparing)="onToolbarPreparing($event)"
            (onInitNewRow)="novoRegistro()"
        >
                <dxo-state-storing
                    [enabled]="true"
                    type="custom"
                    [customLoad]="loadState"
                    [customSave]="saveState"
                    savingTimeout="100"
                ></dxo-state-storing>
                <dxo-export
                    [enabled]="true"
                    [excelWrapTextEnabled]="true"
                    [excelFilterEnabled]="true"
                    fileName="Ícones"
                ></dxo-export>
                <dxo-paging [pageSize]="10"></dxo-paging>
                <dxo-pager
                    [showInfo]="true"
                    [showNavigationButtons]="true"
                    [showPageSizeSelector]="false"
                ></dxo-pager>
        
                <dxo-header-filter [visible]="false"> </dxo-header-filter>
                <dxo-filter-row [visible]="true"></dxo-filter-row>
                <dxo-sorting mode="multiple"></dxo-sorting>
                <dxo-column-chooser [enabled]="false"></dxo-column-chooser>
        
                <dxo-group-panel
                    [visible]="false"
                    [emptyPanelText]="''"
                ></dxo-group-panel>
        
                <dxo-search-panel
                    [visible]="true"
                    placeholder="Buscar configuração de e-mail"
                ></dxo-search-panel>
        
                <dxo-editing
                    mode="form"
                    [allowUpdating]="false"
                    [allowDeleting]="false"
                    [allowAdding]="nivelPermissao === 'FULL'"
                    [useIcons]="true"
                ></dxo-editing>

                <dxi-column
                    name="icone"
                    dataField="codigo"
                    caption="Ícone"
                    cellTemplate="iconColumn"
                    [allowFiltering]="false"
                    [allowSorting]="false"
                ></dxi-column>

                <div *dxTemplate="let data of 'iconColumn'">
                    <i [ngClass]="'fas ' + data.value" style="font-size: 1rem;"></i>
                </div>
        
                <dxi-column
                    name="codigo"
                    dataField="codigo"
                    caption="Código"
                ></dxi-column>
        
                <dxi-column
                    dataField="descricao"
                    caption="descricao"
                ></dxi-column>
        
                <dxi-column
                    dataField="uuid"
                    caption=""
                    [width]="80"
                    [allowFiltering]="false"
                    [allowSorting]="false"
                    cellTemplate="acaoColumn"
                ></dxi-column>
        
                <div *dxTemplate="let data of 'acaoColumn'">
                    <a
                        (click)="alterar(data.value)"
                        class="dx-link dx-link-edit fas fa-edit dx-link-icon btn-icon-grid"
                    >
                    </a>
                    <a
                        *ngIf="nivelPermissao === 'FULL'"
                        (click)="remover(data.value)"
                        class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
                    >
                    </a>
                </div>
        </dx-data-grid>
    </nb-card-body>
  </nb-card>