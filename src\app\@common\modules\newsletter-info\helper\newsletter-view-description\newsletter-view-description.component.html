<div style="height: 200px;" *ngIf="loading$ | async; else content">
  <eqp-loading textLoading="Carregando boletins..."></eqp-loading>
</div>

<ng-template #content>
  <section class="boletim-container">
    <div class="search-container mb-3">
      <div class="search-input-wrapper">
        <input
          type="text"
          class="search-input"
          placeholder="Pesquisar conteúdo"
          [(ngModel)]="termoPesquisa"
          (input)="onPesquisaChange($event.target.value)"
        />
        <button
          *ngIf="termoPesquisa"
          class="clear-search-btn"
          (click)="limparPesquisa()"
          title="Limpar pesquisa"
        >
          <i class="fas fa-times"></i>
        </button>
        <i class="fas fa-search search-icon"></i>
      </div>
    </div>

    <ng-container *ngIf="boletins$ | async as boletins">
      <ng-container *ngIf="boletins.length > 0; else naoHaBoletins">
        <div
          class="boletim-item"
          *ngFor="let boletim of boletins; let i = index"
        >
          <div class="boletim-content mt-2">
            <div class="d-flex justify-content-between">
              <div class="d-flex flex-column">
                <div class="info-row">
                  <span class="label">Título:</span>
                  <span class="value">{{ boletim.titulo }}</span>
                </div>
                <div class="info-row">
                  <span class="label">Data:</span>
                  <span class="value">{{
                    boletim.data | date : 'dd/MM/yyyy - HH:mm'
                  }}</span>
                </div>
              </div>
              <div class="d-flex">
                <button
                  class="view-button"
                  nbButton
                  status="primary"
                  size="small"
                  (click)="visualizarDescricao(boletim.uuid)"
                >
                  <i class="fas fa-eye mr-1"></i>
                  Visualizar descrição
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- Controles de Paginação -->
        <div class="pagination-container" *ngIf="(totalItens$ | async)! > 0">
          <div class="pagination-page-block">
            <div class="pagination-info">
              <span>
                Mostrando {{ ((paginaAtual - 1) * (itensPorPagina$ | async)!) + 1 }} -
                {{ Math.min(paginaAtual * (itensPorPagina$ | async)!, (totalItens$ | async)!) }}
                de {{ totalItens$ | async }} itens
              </span>
            </div>
            <div class="pagination-controls">
              <button
                class="pagination-btn"
                [disabled]="!podeIrParaPaginaAnterior()"
                (click)="irParaPrimeiraPagina()"
                title="Primeira página"
              >
                <i class="fas fa-angle-double-left"></i>
              </button>
  
              <button
                class="pagination-btn"
                [disabled]="!podeIrParaPaginaAnterior()"
                (click)="mudarPagina(paginaAtual - 1)"
                title="Página anterior"
              >
                <i class="fas fa-angle-left"></i>
              </button>
  
              <ng-container *ngFor="let pagina of paginasVisiveis">
                <button
                  *ngIf="pagina !== -1; else ellipsis"
                  class="pagination-btn page-number"
                  [class.active]="pagina === paginaAtual"
                  (click)="mudarPagina(pagina)"
                >
                  {{ pagina }}
                </button>
                <ng-template #ellipsis>
                  <span class="pagination-ellipsis">...</span>
                </ng-template>
              </ng-container>
  
              <button
                class="pagination-btn"
                [disabled]="!podeIrParaProximaPagina()"
                (click)="mudarPagina(paginaAtual + 1)"
                title="Próxima página"
              >
                <i class="fas fa-angle-right"></i>
              </button>
  
              <button
                class="pagination-btn"
                [disabled]="!podeIrParaProximaPagina()"
                (click)="irParaUltimaPagina()"
                title="Última página"
              >
                <i class="fas fa-angle-double-right"></i>
              </button>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #naoHaBoletins>
        <div class="not-found-newsletters">
          <p>Não há boletins cadastrados para o módulo selecionado.</p>
          <i class="fas fa-exclamation-circle"></i>
        </div>
      </ng-template>
    </ng-container>
  </section>
</ng-template>
