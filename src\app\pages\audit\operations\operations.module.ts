import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { OperationsRoutingModule } from './operations-routing.module';
import { CommonToolsModule } from '@common/common-tools.module';
import { OperationsComponent } from './operations-report.component';
import { OperationsReportComponent } from './operations-report/operations-report.component';
import { OperationsDetailComponent } from './operations-detail/operations-detail.component';
import { OperationsHistoricGridComponent } from './operations-historic-grid/operations-historic-grid.component';


@NgModule({
  declarations: [
    OperationsComponent,
    OperationsReportComponent,
    OperationsDetailComponent,
    OperationsHistoricGridComponent
  ],
  imports: [
    CommonModule,
    CommonToolsModule,
    OperationsRoutingModule
  ]
})
export class OperationsModule { }
