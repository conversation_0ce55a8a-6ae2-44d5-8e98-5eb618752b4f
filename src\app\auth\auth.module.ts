import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import { NbLayoutModule, NbSpinnerModule } from '@nebular/theme'

import { EsqueciSenhaComponent } from './esqueci-senha/esqueci-senha.component'
import { LoginComponent } from './login/login.component'
import { ResetarSenhaComponent } from './resetar-senha/resetar-senha.component'
import { SenhaComponent } from './senha/senha.component'

@NgModule({
  declarations: [
    LoginComponent,
    SenhaComponent,
    EsqueciSenhaComponent,
    ResetarSenhaComponent,
  ],
  imports: [CommonModule, CommonToolsModule, NbSpinnerModule, NbLayoutModule],
  exports: [
    LoginComponent,
    SenhaComponent,
    EsqueciSenhaComponent,
    ResetarSenhaComponent,
  ],
})
export class AuthModule {}
