import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelect } from '@design-tools/interfaces/nebular-select'
import { NbDialogService } from '@nebular/theme'
import { UsersService } from '@pages/administration/users/users.service'
import { Subscription } from 'rxjs'
import { first } from 'rxjs/operators'

import { MenuService } from './../../../menu.service'

@Component({
  selector: 'eqp-users-list',
  templateUrl: './users-list.component.html',
  styleUrls: ['./users-list.component.scss'],
})
export class UsersListComponent
  extends BaseTelasComponent
  implements OnInit, <PERSON><PERSON><PERSON>roy
{
  public loading: boolean = false
  public pageTitle: string = 'Administração | Usuários'

  public gridData: any

  public statuses: NebularSelect[] = [
    {
      texto: 'Ativo',
      valor: 'ACTIVE',
    },
    {
      texto: 'Inativo',
      valor: 'INACTIVE',
    },
    {
      texto: 'Bloqueado',
      valor: 'BLOCKED',
    },
  ]

  private subscription: Subscription
  constructor(
    private service: UsersService,
    private toastr: ToastrService,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
    this.permissao('/administracao/usuarios')
  }

  public ngOnInit(): void {
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      'usuario/listagem-grid',
      10,
    )
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.name === 'addRowButton') {
        item.showText = 'ever'
        item.options.text = 'Novo usuário'
        item.options.hint = 'Novo usuário'
        item.options.onClick = () => this.novoRegistro()
      }

      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`administracao/usuarios/novo`])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([`administracao/usuarios/edit/${uuid}`])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Usuário excluído com sucesso.',
              })
              this.gridData = this.service.getDataSourceFiltro(
                'uuid',
                'usuario',
                10,
              )
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
