<dx-data-grid
  #dataGrid
  id="parametersGrid"
  [dataSource]="gridData"
  [allowColumnReordering]="true"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="loading"
  [columnHidingEnabled]="true"
  keyExpr="uuid"
  (onToolbarPreparing)="onToolbarPreparing($event)"
  (onRowInserting)="onRowinserting($event)"
  (onRowUpdating)="onRowUpdating($event)"
  (onRowRemoving)="onRowRemoving($event)"
  (onRowRemoved)="onRowRemoved($event)"
  (onEditingStart)="bloquearFuncoes(true)"
  (onInitNewRow)="bloquearFuncoes(true)"
  (onEditCanceling)="bloquearFuncoes(false)"
>
  <dxo-export
    [enabled]="true"
    [excelWrapTextEnabled]="true"
    [excelFilterEnabled]="true"
    fileName="Controle de acesso | Parâmetros"
  ></dxo-export>

  <dxo-paging [pageSize]="10"></dxo-paging>
  <dxo-pager
    [showInfo]="true"
  >
  </dxo-pager>

  <dxo-filter-row [visible]="!bloqueia"></dxo-filter-row>
  <!-- <dxo-filter-panel [visible]="true"></dxo-filter-panel>
  <dxo-filter-builder [allowHierarchicalFields]="true">
  </dxo-filter-builder> -->

  <dxo-sorting mode="multiple"></dxo-sorting>

  <dxo-column-chooser [enabled]="true"></dxo-column-chooser>

  <dxo-group-panel
    [visible]="true"
    [emptyPanelText]="''"
  ></dxo-group-panel>

  <dxo-search-panel
    [visible]="!bloqueia"
    placeholder="Buscar parâmetro"
  ></dxo-search-panel>
  <dxo-sorting [mode]="!bloqueia ? 'multiple': 'none' ">
  </dxo-sorting>
  <!--
  <dxo-load-panel [enabled]="loading">

  </dxo-load-panel> -->

  <dxo-editing
    mode="form"
    [allowUpdating]="true"
    [allowDeleting]="true"
    [allowAdding]="true"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column
    *ngFor="let column of columnsTemplate"
    [dataField]="column?.dataField"
    [caption]="column?.caption"
    [hidingPriority]="column?.hidingPriority"
    [dataType]="column?.dataType"
    [sortOrder]="column?.sortOrder"
    [visible]="column?.visible"
    [cellTemplate]="column?.cellTemplate"
    [allowEditing]="column?.allowEditing"
    [allowExporting]="column?.allowExporting"
    [allowFiltering]="column?.allowFiltering"
    [allowHiding]="column?.allowHiding"
    [allowResizing]="column?.allowResizing"
    [allowSorting]="column?.allowSorting"
    [allowSearch]="column?.allowSearch"
    [allowGrouping]="column?.allowGrouping"
    [allowHeaderFiltering]="column?.allowHeaderFiltering"
    [allowReordering]="column?.allowReordering"
    [editorOptions]="column?.editorOptions"
    [width]="column?.width"
    [alignment]="column?.alignment"
    [type]="column?.type"
    [customizeText]="column?.customizeText"
  >
    <dxo-lookup
      *ngIf="column?.lookup"
      [dataSource]="column?.lookup?.dataSource"
      [displayExpr]="column?.lookup?.displayExpr"
      [valueExpr]="column?.lookup?.valueExpr"
    >
    </dxo-lookup>

    <dxi-button
      *ngFor="let button of column?.buttons"
      [name]="button?.name"
      [icon]="button?.icon"
      [cssClass]="button?.cssClass"
    ></dxi-button>

    <dxo-form-item
      *ngIf="column?.formItem"
      [colSpan]="column?.formItem?.colSpan"
      [editorOptions]="column?.formItem?.editorOptions"
      [editorType]="column?.formItem?.editorType"
      [isRequired]="column?.formItem?.isRequired"
      [visible]="column?.formItem?.visible"
    >
    </dxo-form-item>
  </dxi-column>
</dx-data-grid>
