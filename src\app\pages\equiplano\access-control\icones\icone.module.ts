import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { IconeListagemComponent } from './icone-listagem/icone-listagem.component';
import { IconeRoutingModule } from './icone-routing.module';
import { CommonToolsModule } from '@common/common-tools.module';
import { IconeComponent } from './icone.component';
import { IconeFormComponent } from './icone-form/icone-form.component';
import { DxButtonModule } from 'devextreme-angular/ui/button';


@NgModule({
  declarations: [
    IconeComponent,
    IconeListagemComponent,
    IconeFormComponent,
  ],
  imports: [
    CommonModule,
    IconeRoutingModule,
    CommonToolsModule,
    DxButtonModule
  ]
})
export class IconeModule { }
