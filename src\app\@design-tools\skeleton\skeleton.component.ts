import { Component, Input } from '@angular/core';

@Component({
  selector: 'eqp-skeleton',
  templateUrl: './skeleton.component.html',
  styleUrls: ['./skeleton.component.scss']
})
export class SkeletonComponent {

  @Input() width = '100px';
  @Input() maxWidth = '100%';
  @Input() height = '100px';
  @Input() maxHeight = '100%';
  @Input() radius = "16px";
  @Input() class = "";
  @Input() style = "";

  @Input()
  public textLoading?: string;

  @Input()
  public textLoadingAriaLabel?: string;
}
