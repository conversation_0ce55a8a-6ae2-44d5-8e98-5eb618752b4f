import { Component, Input, OnInit, On<PERSON><PERSON><PERSON>, ElementRef } from '@angular/core'
import { NbAccordionComponent, NbDialogService } from '@nebular/theme'
import { NewsletterInterface } from '@pages/newsletter/newsletter'
import { NewsletterService } from '@pages/newsletter/newsletter.service'
import {
  debounceTime,
  map,
  shareReplay,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators'
import { NewsletterViewMessageComponent } from '../newsletter-view-message/newsletter-view-message.component'
import { NewsletterStorageService } from '@core/utils/newsletter-storage.service'
import { normalizarString } from '@common/helpers/string-normalizer'
import { BehaviorSubject, combineLatest, Subject } from 'rxjs'
import { Observable } from 'rxjs-compat'

@Component({
  selector: 'eqp-newsletter-view-description',
  templateUrl: './newsletter-view-description.component.html',
  styleUrls: ['./newsletter-view-description.component.scss'],
})
export class NewsletterViewDescriptionComponent implements OnInit, OnD<PERSON>roy {
  @Input() modulo: any
  @Input() termoPesquisa$: BehaviorSubject<string> = new BehaviorSubject('')

  // Observables para controle de estado
  private destroy$ = new Subject<void>()
  paginaAtual$ = new BehaviorSubject(1)
  itensPorPagina$ = new BehaviorSubject(10)
  totalItens$ = new BehaviorSubject(0)
  loading$ = new BehaviorSubject(false)

  // Propriedades para o template
  boletins$: Observable<NewsletterInterface[]>
  paginaAtual = 1
  totalPaginas = 0
  paginasVisiveis: number[] = []

  // Expor Math para o template
  Math = Math

  constructor(
    private newsletterService: NewsletterService,
    private newsletterStorageService: NewsletterStorageService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    const moduloLogadoNome = normalizarString(
      this.newsletterService.obterNomeModulo(),
    )
    const moduloNome = normalizarString(this.modulo.descricao)
    if (moduloLogadoNome && moduloNome === moduloLogadoNome) {
      this.newsletterStorageService.restore()
    }

    // Configurar o observable dos boletins com filtro de pesquisa
    this.boletins$ = combineLatest([
      this.paginaAtual$,
      this.itensPorPagina$,
      this.termoPesquisa$
    ]).pipe(
      debounceTime(300),
      tap(() => this.loading$.next(true)),
      switchMap(([pagina, tamanho, termoPesquisa]) => {
        const skip = (pagina - 1) * tamanho
        return this.newsletterService.getAllByModule({
          moduloUuid: this.modulo.uuid,
          skip,
          take: tamanho
        }).pipe(
          map(response => {
            // Filtrar no frontend se houver termo de pesquisa
            if (termoPesquisa && termoPesquisa.trim()) {
              const termoLower = termoPesquisa.toLowerCase().trim()
              const dadosFiltrados = response.data.filter(boletim =>
                boletim.titulo?.toLowerCase().includes(termoLower)
              )
              return {
                ...response,
                data: dadosFiltrados,
                totalCount: dadosFiltrados.length
              }
            }
            return response
          })
        )
      }),
      tap(response => {
        this.totalItens$.next(response.totalCount || 0)
        this.totalPaginas = Math.ceil(
          (response.totalCount || 0) / this.itensPorPagina$.value,
        )
        this.atualizarPaginasVisiveis()
        this.loading$.next(false)
        this.scrollParaAccordion()
      }),
      map(response => response.data),
      shareReplay(1),
    )

    // Observar mudanças na página atual
    this.paginaAtual$.pipe(takeUntil(this.destroy$)).subscribe(pagina => {
      this.paginaAtual = pagina
      this.atualizarPaginasVisiveis()
    })

    // Observar mudanças no tamanho da página
    this.itensPorPagina$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.totalPaginas = Math.ceil(
        this.totalItens$.value / this.itensPorPagina$.value,
      )
      this.atualizarPaginasVisiveis()
    })

    // Resetar para primeira página quando pesquisar
    this.termoPesquisa$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.paginaAtual$.next(1)
    })
  }

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  private scrollParaAccordion() {
    queueMicrotask(() => {
      const el = document.getElementById(`accordion-${this.modulo.uuid}`)
      console.log(el)
      el.scrollIntoView({ behavior: 'smooth' })
    })
  }

  mudarPagina(novaPagina: number): void {
    if (novaPagina >= 1 && novaPagina <= this.totalPaginas) {
      this.paginaAtual$.next(novaPagina)
    }
  }

  mudarTamanho(novoTamanho: number): void {
    this.itensPorPagina$.next(novoTamanho)
    this.paginaAtual$.next(1)
  }

  podeIrParaPaginaAnterior(): boolean {
    return this.paginaAtual > 1
  }

  podeIrParaProximaPagina(): boolean {
    return this.paginaAtual < this.totalPaginas
  }

  irParaPrimeiraPagina(): void {
    this.mudarPagina(1)
  }

  irParaUltimaPagina(): void {
    this.mudarPagina(this.totalPaginas)
  }

  visualizarDescricao(boletimUuid: string) {
    this.dialogService.open(NewsletterViewMessageComponent, {
      context: {
        boletimUuid,
        moduloNome: this.modulo.descricao,
      },
    })
  }

  atualizarPaginasVisiveis(): void {
    const paginas: number[] = []

    if (this.totalPaginas <= 7) {
      // Se temos 7 páginas ou menos, mostra todas
      for (let i = 1; i <= this.totalPaginas; i++) {
        paginas.push(i)
      }
    } else {
      // Sempre mostra a primeira página
      paginas.push(1)

      // Adiciona "..." se necessário
      if (this.paginaAtual > 4) {
        paginas.push(-1) // representa "..."
      }

      // Calcula o range de páginas ao redor da página atual
      const start = Math.max(2, this.paginaAtual - 1)
      const end = Math.min(this.totalPaginas - 1, this.paginaAtual + 1)

      for (let i = start; i <= end; i++) {
        if (!paginas.includes(i)) {
          paginas.push(i)
        }
      }

      // Adiciona "..." se necessário
      if (this.paginaAtual < this.totalPaginas - 3) {
        paginas.push(-1) // representa "..."
      }

      // Sempre mostra a última página
      if (this.totalPaginas > 1 && !paginas.includes(this.totalPaginas)) {
        paginas.push(this.totalPaginas)
      }
    }

    this.paginasVisiveis = paginas
  }
}
