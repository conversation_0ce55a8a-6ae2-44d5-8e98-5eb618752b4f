import { Component, Input, OnInit } from '@angular/core'
import { NbDialogService } from '@nebular/theme'
import { NewsletterInterface } from '@pages/newsletter/newsletter'
import { NewsletterService } from '@pages/newsletter/newsletter.service'
import { debounceTime, finalize, map, shareReplay, switchMap, takeUntil, tap } from 'rxjs/operators'
import { NewsletterViewMessageComponent } from '../newsletter-view-message/newsletter-view-message.component'
import { NewsletterStorageService } from '@core/utils/newsletter-storage.service'
import { normalizarString } from '@common/helpers/string-normalizer'
import DataSource from 'devextreme/data/data_source'
import { CrudService } from '@common/services/crud.service'
import { BehaviorSubject, combineLatest, of, Subject } from 'rxjs'
import { Observable } from 'rxjs-compat'
// import { DxPaginationModule } from "devextreme-angular" // Disponível apenas na versão 24.2+


@Component({
  selector: 'eqp-newsletter-view-description',
  templateUrl: './newsletter-view-description.component.html',
  styleUrls: ['./newsletter-view-description.component.scss'],
})
export class NewsletterViewDescriptionComponent implements OnInit {
  @Input() modulo: any
  public loading = false
  public boletins: NewsletterInterface[] = []
  private destroy$ = new Subject<void>()
  dataSource: DataSource

  paginaAtual$ = new BehaviorSubject(1);
  itensPorPagina$ = new BehaviorSubject(10);

  boletins$: Observable<NewsletterInterface[]>;
  totalItens$ = new BehaviorSubject(0);
  loading$ = new BehaviorSubject(false);
  paginaAtual = 1;

  constructor(
    private newsletterService: NewsletterService,
    private newsletterStorageService: NewsletterStorageService,
    private dialogService: NbDialogService,
    private crudService: CrudService,    
  ) {
     this.boletins$ = combineLatest([
      this.paginaAtual$,
      this.itensPorPagina$
    ]).pipe(
      debounceTime(300),
      tap(() => this.loading$.next(true)),
      switchMap(([pagina, tamanho]) => {
        const skip = (pagina - 1) * tamanho;
        return this.newsletterService.getAllByModule({
          moduloUuid: this.modulo.uuid, 
          skip, 
          take: tamanho,
        });
      }),
      tap((response) => {
        this.totalItens$.next(response.totalCount || 0);
        this.loading$.next(false);
      }),
      map((response) => response.data),
      shareReplay(1)
    );

    this.paginaAtual$
    .pipe(takeUntil(this.destroy$))
    .subscribe(pagina => {
      this.paginaAtual = pagina;
    });
  }

  mudarPagina(novaPagina: number): void {
    this.paginaAtual$.next(novaPagina);
  }

  mudarTamanho(novoTamanho: number): void {
    this.itensPorPagina$.next(novoTamanho);
    this.paginaAtual$.next(1); 
  }

  ngOnInit(): void {
    const moduloLogadoNome = normalizarString(this.newsletterService.obterNomeModulo())
    const moduloNome = normalizarString(this.modulo.descricao)
    if(moduloLogadoNome && (moduloNome === moduloLogadoNome)) {
      this.newsletterStorageService.restore()
    }
    

    this.dataSource = new DataSource({
      store: this.crudService.getDataSourceFiltro(
        'uuid',
        'boletim-informativo',
        10,
        'moduloUuid',
        this.modulo.uuid,
      ),
      paginate: true,
      pageSize: 10,
    })
    // this.loading = true
    // this.newsletterService
    //   .getAllByModule(this.modulo.uuid)
    //   .pipe(finalize(() => (this.loading = false)))
    //   .subscribe(boletins => {
    //     this.boletins = boletins
    //   })
  }

  obterPorPagina() {
    return of(this.dataSource.store().load())
  }

  visualizarDescricao(boletimUuid: string) {
    this.dialogService.open(NewsletterViewMessageComponent, {
      context: {
        boletimUuid,
        moduloNome: this.modulo.descricao,
      },
    })
  }

  atualizarPaginasVisiveis() {
  const paginas = [];

  if (this.totalPaginas <= 7) {
    for (let i = 1; i <= this.totalPaginas; i++) {
      paginas.push(i);
    }
  } else {
    paginas.push(1);
    if (this.paginaAtual > 4) paginas.push(-1); // representa "..."

    const start = Math.max(2, this.paginaAtual - 1);
    const end = Math.min(this.totalPaginas - 1, this.paginaAtual + 1);

    for (let i = start; i <= end; i++) {
      paginas.push(i);
    }

    if (this.paginaAtual < this.totalPaginas - 3) paginas.push(-1); // representa "..."
    paginas.push(this.totalPaginas);
  }

  this.paginasVisiveis = paginas;
}
  
}
