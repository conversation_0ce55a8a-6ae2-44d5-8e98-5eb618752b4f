import { Component, Input, OnInit, On<PERSON><PERSON>roy, ElementRef } from '@angular/core'
import { NbAccordionComponent, NbDialogService } from '@nebular/theme'
import { NewsletterInterface } from '@pages/newsletter/newsletter'
import { NewsletterService } from '@pages/newsletter/newsletter.service'
import {
  debounceTime,
  delay,
  map,
  shareReplay,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators'
import { NewsletterViewMessageComponent } from '../newsletter-view-message/newsletter-view-message.component'
import { NewsletterStorageService } from '@core/utils/newsletter-storage.service'
import { normalizarString } from '@common/helpers/string-normalizer'
import { BehaviorSubject, combineLatest, Subject, timer } from 'rxjs'
import { Observable } from 'rxjs-compat'

@Component({
  selector: 'eqp-newsletter-view-description',
  templateUrl: './newsletter-view-description.component.html',
  styleUrls: ['./newsletter-view-description.component.scss'],
})
export class NewsletterViewDescriptionComponent implements OnInit, OnD<PERSON>roy {
  @Input() modulo: any

  private destroy$ = new Subject<void>()
  paginaAtual$ = new BehaviorSubject(1)
  itensPorPagina$ = new BehaviorSubject(10)
  totalItens$ = new BehaviorSubject(0)
  loading$ = new BehaviorSubject(true)
  termoPesquisa$ = new BehaviorSubject<string>('')

  boletins$: Observable<NewsletterInterface[]>
  paginaAtual = 1
  totalPaginas = 0
  paginasVisiveis: number[] = []
  termoPesquisa = ''

  Math = Math

  constructor(
    private newsletterService: NewsletterService,
    private newsletterStorageService: NewsletterStorageService,
    private dialogService: NbDialogService,
  ) {}

  ngOnInit(): void {
    const moduloLogadoNome = normalizarString(
      this.newsletterService.obterNomeModulo(),
    )
    const moduloNome = normalizarString(this.modulo.descricao)
    if (moduloLogadoNome && moduloNome === moduloLogadoNome) {
      this.newsletterStorageService.restore()
    }

    this.boletins$ = combineLatest([
      this.paginaAtual$,
      this.itensPorPagina$,
      this.termoPesquisa$
    ]).pipe(
      debounceTime(1000),
      tap(() => this.loading$.next(true)),
      switchMap(([pagina, tamanho, termoPesquisa]) => {
        const skip = (pagina - 1) * tamanho

        const loadingTimer$ = timer(1000)
        const dataRequest$ = this.newsletterService.getAllByModule({
          moduloUuid: this.modulo.uuid,
          skip,
          take: tamanho,
          termoPesquisa
        })
        return combineLatest([loadingTimer$, dataRequest$]).pipe(
          map(([_, response]) => response)
        )
      }),
      tap(response => {
        this.totalItens$.next(response.totalCount || 0)
        this.totalPaginas = Math.ceil(
          (response.totalCount || 0) / this.itensPorPagina$.value,
        )
        this.atualizarPaginasVisiveis()
        this.loading$.next(false)
        this.scrollParaAccordion()
      }),
      map(response => response.data),
      shareReplay(1),
    )

    this.paginaAtual$.pipe(takeUntil(this.destroy$)).subscribe(pagina => {
      this.paginaAtual = pagina
      this.atualizarPaginasVisiveis()
    })

    this.itensPorPagina$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.totalPaginas = Math.ceil(
        this.totalItens$.value / this.itensPorPagina$.value,
      )
      this.atualizarPaginasVisiveis()
    })

    this.termoPesquisa$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.paginaAtual$.next(1)
    })

    // Forçar o carregamento inicial dos dados
    // Isso é necessário porque o loading$ começa como true
    this.boletins$.pipe(takeUntil(this.destroy$)).subscribe()
  }

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  private scrollParaAccordion() {
    queueMicrotask(() => {
      const el = document.getElementById(`accordion-${this.modulo.uuid}`)
      el.scrollIntoView({ behavior: 'smooth' })
    })
  }

  mudarPagina(novaPagina: number): void {
    if (novaPagina >= 1 && novaPagina <= this.totalPaginas) {
      this.paginaAtual$.next(novaPagina)
    }
  }

  mudarTamanho(novoTamanho: number): void {
    this.itensPorPagina$.next(novoTamanho)
    this.paginaAtual$.next(1)
  }

  podeIrParaPaginaAnterior(): boolean {
    return this.paginaAtual > 1
  }

  podeIrParaProximaPagina(): boolean {
    return this.paginaAtual < this.totalPaginas
  }

  irParaPrimeiraPagina(): void {
    this.mudarPagina(1)
  }

  irParaUltimaPagina(): void {
    this.mudarPagina(this.totalPaginas)
  }

  visualizarDescricao(boletimUuid: string) {
    this.dialogService.open(NewsletterViewMessageComponent, {
      context: {
        boletimUuid,
        moduloNome: this.modulo.descricao,
      },
    })
  }

  onPesquisaChange(termo: string): void {
    this.termoPesquisa = termo
    this.termoPesquisa$.next(termo)
  }

  limparPesquisa(): void {
    this.termoPesquisa = ''
    this.termoPesquisa$.next('')
  }

  atualizarPaginasVisiveis(): void {
    const paginas: number[] = []

    if (this.totalPaginas <= 7) {
      for (let i = 1; i <= this.totalPaginas; i++) {
        paginas.push(i)
      }
    } else {
      paginas.push(1)

      if (this.paginaAtual > 4) {
        paginas.push(-1) 
      }

      const start = Math.max(2, this.paginaAtual - 1)
      const end = Math.min(this.totalPaginas - 1, this.paginaAtual + 1)

      for (let i = start; i <= end; i++) {
        if (!paginas.includes(i)) {
          paginas.push(i)
        }
      }

      if (this.paginaAtual < this.totalPaginas - 3) {
        paginas.push(-1) // representa "..."
      }

      if (this.totalPaginas > 1 && !paginas.includes(this.totalPaginas)) {
        paginas.push(this.totalPaginas)
      }
    }

    this.paginasVisiveis = paginas
  }
}
