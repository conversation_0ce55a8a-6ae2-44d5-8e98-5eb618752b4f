import { Component, Input, OnInit } from '@angular/core'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { UsersService } from '../users.service'

@Component({
  selector: 'eqp-grupo-acesso-lista',
  templateUrl: './grupo-acesso-lista.component.html',
  styleUrls: ['./grupo-acesso-lista.component.scss'],
})
export class GrupoAcessoListaComponent implements OnInit {
  @Input()
  public user: any
  public gridData: any
  selectedItemKeys: string[] = []

  public constructor(
    protected ref: NbDialogRef<GrupoAcessoListaComponent>,
    private service: UsersService,
  ) {}

  ngOnInit(): void {
    this.fetchGrid()
  }

  private fetchGrid(): void {
    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltroCondicao(
        'uuid',
        `usuario/grupo-acesso/listagem-grid`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public dismiss(): void {
    this.ref.close([])
  }

  onSelectionChanged({ selectedRowKeys }: any): void {
    this.selectedItemKeys = selectedRowKeys
  }

  public selecionar(): void {
    this.ref.close(this.selectedItemKeys)
  }
}
