import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import {
  MicroServicesInterface,
  MicroServicesReturnInterface,
} from '@pages/equiplano/access-control/micro-services/micro-service'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class MicroServicesService {
  constructor(private http: HttpClient) {}

  public get(): Observable<MicroServicesReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<MicroServicesReturnInterface>(`microservico`, {
      headers,
    })
  }

  public getIndividual(uuid: string): Observable<MicroServicesInterface[]> {
    const headers = new HttpHeaders()

    return this.http.get<MicroServicesInterface[]>(`microservico/${uuid}`, {
      headers,
    })
  }

  public put(dto: MicroServicesInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<MicroServicesInterface>(
      `microservico/${dto.uuid}`,
      dto,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public post(dto: MicroServicesInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<MicroServicesInterface>(`microservico`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: MicroServicesInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<MicroServicesInterface[]>(
      `microservico/lote`,
      batch,
      {
        headers,
        observe: 'response',
      },
    )
  }

  public delete(uuid: number): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`microservico/${uuid}`, {
      headers,
    })
  }
}
