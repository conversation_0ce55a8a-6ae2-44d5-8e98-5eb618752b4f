<dx-data-grid
  id="permissionsGrid"
  [dataSource]="gridData"
  [allowColumnResizing]="true"
  [columnAutoWidth]="true"
  [showColumnLines]="false"
  [showRowLines]="false"
  [showBorders]="false"
  [rowAlternationEnabled]="true"
  [wordWrapEnabled]="true"
  [loadPanel]="false"
  [columnHidingEnabled]="true"
  keyExpr="uuid"
  (onToolbarPreparing)="onToolbarPreparing($event)"
  (onInitNewRow)="novoRegistro()"
>
  <dxo-export
    [enabled]="true"
    fileName="Controle de acesso | Permissões"
  ></dxo-export>

  <dxo-paging [pageSize]="10"></dxo-paging>
  <dxo-pager
    [showInfo]="true"
    [showNavigationButtons]="true"
    [showPageSizeSelector]="false"
  >
  </dxo-pager>

  <dxo-header-filter [visible]="false"> </dxo-header-filter>
  <dxo-filter-row [visible]="true"></dxo-filter-row>

  <dxo-sorting mode="multiple"></dxo-sorting>
  <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

  <dxo-group-panel [visible]="false" [emptyPanelText]="''"></dxo-group-panel>

  <dxo-search-panel
    [visible]="true"
    placeholder="Buscar permissão"
  ></dxo-search-panel>

  <dxo-editing
    mode="form"
    [allowUpdating]="false"
    [allowDeleting]="false"
    [allowAdding]="true"
    [useIcons]="true"
  >
  </dxo-editing>

  <dxi-column dataField="acaoNome" caption="Ação"></dxi-column>
  <dxi-column
    dataField="nivelAcesso"
    caption="Nível de acesso"
    cellTemplate="nivelColumn"
  >
    <dxo-lookup
      [dataSource]="nivelAcessoData"
      displayExpr="nome"
      valueExpr="valor"
    >
    </dxo-lookup>
  </dxi-column>
  <dxi-column
    dataField="uuid"
    caption=""
    [width]="80"
    [allowFiltering]="false"
    [allowSorting]="false"
    cellTemplate="acaoColumn"
  ></dxi-column>
  <div *dxTemplate="let data of 'acaoColumn'">
    <a
      *ngIf="nivelPermissao === 'FULL'"
      (click)="remover(data.value)"
      class="dx-link dx-link-delete fas fa-trash-alt dx-link-icon btn-icon-grid"
    >
    </a>
  </div>
  <div *dxTemplate="let data of 'nivelColumn'">
    {{ mostrarNivel(data) }}
  </div>
</dx-data-grid>
