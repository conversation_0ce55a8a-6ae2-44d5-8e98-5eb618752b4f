import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'

import { ClientsComponent } from './clients/client/clients.component'
import {
  ClientsListComponent,
} from './clients/clients-list/clients-list.component'
import { EquiplanoComponent } from './equiplano.component'

const routes: Routes = [
  {
    path: '',
    component: EquiplanoComponent,
    children: [
      {
        path: 'clientes',
        component: ClientsListComponent,
      },
      {
        path: 'clientes/novo',
        component: ClientsComponent,
      },
      {
        path: 'clientes/edit/:uuid',
        component: ClientsComponent,
      },
      {
        path: 'county-client',
        loadChildren: () =>
          import('./county-client/county-client.module').then(
            m => m.CountyClientModule,
          ),
      },
      {
        path: 'controle-acesso',
        loadChildren: () =>
          import('./access-control/access-control.module').then(
            m => m.AccessControlModule,
          ),
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class EquiplanoRoutingModule {}
