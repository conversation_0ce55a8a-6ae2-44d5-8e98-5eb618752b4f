import { Component, Input, OnInit } from '@angular/core';
import DataSource from 'devextreme/data/data_source';

@Component({
  selector: 'eqp-operations-detail',
  templateUrl: './operations-detail.component.html',
  styleUrls: ['./operations-detail.component.scss']
})
export class OperationsDetailComponent implements OnInit {

  @Input() logAccessData: any[] = []
  gridData: DataSource
  constructor() { }

  ngOnInit(): void {
    this.gridData = new DataSource({
      store: {
        data: this.logAccessData || [],
        key: 'uuid',
        type: 'array'
      },
      sort: [{selector: 'data', desc: true}],
      pageSize: 10,
      paginate: true
    })
  }
}
