import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { MenuService } from '@pages/menu.service';
import CustomStore from 'devextreme/data/custom_store';
import { first } from 'rxjs/operators';
import { IconeService } from '../services/icon.service';

@Component({
  selector: 'eqp-icone-listagem',
  templateUrl: './icone-listagem.component.html',
  styleUrls: ['./icone-listagem.component.scss'],
})
export class IconeListagemComponent extends BaseTelasComponent implements OnInit {

  public gridData: CustomStore;
  public readonly pageTitle = 'Ícones';

  constructor (
    private router: Router,
    private service: IconeService,
    public menuService: MenuService,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {
    super(menuService);
  }

  public ngOnInit(): void {
    this.getGridData();
  }

  private getGridData() {
    this.gridData = this.service.getDataSourceFiltro(
      'uuid',
      'icone',
      10,
    );
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items[0].showText = 'ever'
    event.toolbarOptions.items[0].options.text = 'Novo ícone'
    event.toolbarOptions.items[0].options.hint = 'Novo ícone'

    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`/equiplano/controle-acesso/icones/novo`])
  }

  public alterar(codigo: string): void {
    this.gravarParametros();
    this.router.navigate([`/equiplano/controle-acesso/icones/edit/${codigo}`])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Ícone excluído com sucesso.',
              })
              this.getGridData();
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

}
