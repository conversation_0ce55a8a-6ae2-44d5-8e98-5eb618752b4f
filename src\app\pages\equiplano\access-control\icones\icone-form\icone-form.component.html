<nb-card>
    <nb-card-header>
      <div class="row">
        <div class="col-md-8">
          <h5>{{ pageTitle }}</h5>
        </div>
      </div>
        <div class="mt-2">
            <eqp-breadcrumb></eqp-breadcrumb>
        </div>
    </nb-card-header>
    <nb-card-body>
        <div *ngIf="!!!controls['uuid'].value" class="d-flex justify-content-end">
            <div class="w-auto dx-field-value">
                <a href="https://fontawesome.com/search?o=r&m=free&s=solid" target="_blank">
                    <dx-button
                        icon="fa-solid fa-circle-info"
                        nbTooltip="Buscar código no FontAwesome"
                        nbTooltipPlacement="left"
                    ></dx-button>
                </a>
            </div>
        </div>
        <div class="row" [formGroup]="formulario">
            <div class="col-md-6 col-sm-12 mb-4">
                <eqp-nebular-input
                  [style]="'basic'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="codigo"
                  name="Código"
                  label="Código"
                  placeholder="Código"
                  maxlength="30"
                  [disabled]="!!controls['uuid'].value"
                ></eqp-nebular-input>
            </div>
            <div class="col-md-6 col-sm-12 mb-4">
                <eqp-nebular-input
                  [style]="'basic'"
                  [size]="'small'"
                  [shape]="'rectangle'"
                  formControlName="descricao"
                  name="Descricao"
                  label="Descricao"
                  placeholder="Descricao"
                  maxlength="50"
                ></eqp-nebular-input>
            </div>
            <div *ngIf="iconePreview" class="col-md-6 col-sm-12 mb-4">
                <label>Ícone preview</label>
                <div class="container-icon">
                    <i [class]="iconePreview"></i>
                </div>
                <span *ngIf="!!!controls['uuid'].value" class="text-warning">
                    <i class="fa fa-triangle-exclamation"></i>
                    Verifique o código se o ícone não aparecer.
                </span>
            </div>
        </div>
    </nb-card-body>

    <nb-card-footer>
        <div class="row">
            <div class="col-md-12">
                <button type="button" class="btn btn-dark" (click)="cancelar()">
                Voltar
                </button>
                <button
                    *ngIf="formulario.get('uuid').value"
                    type="button"
                    class="btn btn-danger ml-3 float-md-right"
                    (click)="remover()"
                >
                    Apagar
                </button>
                <button
                    type="button"
                    class="btn btn-success float-md-right"
                    (click)="gravar()"
                >
                    Gravar
                </button>   
            </div>
        </div>
    </nb-card-footer>
</nb-card>
