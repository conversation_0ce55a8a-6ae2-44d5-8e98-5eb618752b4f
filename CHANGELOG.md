<a name="1.0.0"></a>

# [1.0.0]

### Features

- **creation:** Project created
- **initial:** Added initial features

<a name="1.0.21"></a>

## <small>1.0.21 (2021-11-18)</small>

- Entity selection changes

<a name="1.0.20"></a>

## <small>1.0.20 (2021-07-28)</small>

- Adding profile TODO's and menu adjustments ([dcb7ab7](https://gitlab.equiplano.com.br/front-end/central/commit/dcb7ab7))
- BOFFICE-01 - Adding features and translate correction ([c3671ec](https://gitlab.equiplano.com.br/front-end/central/commit/c3671ec))
- Client feature finished ([e239fa1](https://gitlab.equiplano.com.br/front-end/central/commit/e239fa1))
- Client list and crud creation ([2667cad](https://gitlab.equiplano.com.br/front-end/central/commit/2667cad))
- CORS Correction ([82b10c5](https://gitlab.equiplano.com.br/front-end/central/commit/82b10c5))
- Enviroment adjustments ([08ec637](https://gitlab.equiplano.com.br/front-end/central/commit/08ec637))
- Enviroment provisionment ([5e21ba0](https://gitlab.equiplano.com.br/front-end/central/commit/5e21ba0))
- Enviroment provisionment ([b6eb2f3](https://gitlab.equiplano.com.br/front-end/central/commit/b6eb2f3))
- Headers adjustment ([060ba15](https://gitlab.equiplano.com.br/front-end/central/commit/060ba15))
- Headers adjustment for deployed version ([a3f6cad](https://gitlab.equiplano.com.br/front-end/central/commit/a3f6cad))
- Headers adjustments ([41f92b3](https://gitlab.equiplano.com.br/front-end/central/commit/41f92b3))
- Initial structure ([4425b78](https://gitlab.equiplano.com.br/front-end/central/commit/4425b78))
- Last features added ([8f8ba74](https://gitlab.equiplano.com.br/front-end/central/commit/8f8ba74))
- Last features adjustments ([e13f624](https://gitlab.equiplano.com.br/front-end/central/commit/e13f624))
- Last features adjustments ([d77c646](https://gitlab.equiplano.com.br/front-end/central/commit/d77c646))
- Login structure ([df6dfef](https://gitlab.equiplano.com.br/front-end/central/commit/df6dfef))
- Login structure ([79798dc](https://gitlab.equiplano.com.br/front-end/central/commit/79798dc))
- Update docker-compose.yml ([20cb905](https://gitlab.equiplano.com.br/front-end/central/commit/20cb905))
- Update README.md ([38ce94d](https://gitlab.equiplano.com.br/front-end/central/commit/38ce94d)
