import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { getMomentDate, getParsedDate } from '@common/helpers/parsers'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NebularSelect } from '@design-tools/interfaces/nebular-select'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { DxDataGridComponent } from 'devextreme-angular'
import DataSource from 'devextreme/data/data_source'
import { finalize, first } from 'rxjs/operators'

import { AnexoComponent } from '../anexo/anexo.component'
import { UsersService } from '../users.service'

@Component({
  selector: 'eqp-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss'],
})
export class UsersComponent
  extends BaseTelasComponent
  implements OnInit, AfterViewInit
{
  public loading: boolean = false
  public pageTitle: string = 'Administração | Usuários'
  public formulario: FormGroup

  public statuses: NebularSelect[] = [
    {
      texto: 'Ativo',
      valor: 'ACTIVE',
    },
    {
      texto: 'Inativo',
      valor: 'INACTIVE',
    },
    {
      texto: 'Bloqueado',
      valor: 'BLOCKED',
    },
  ]

  public gruposAcesso: any

  public gridBoxValue: any[] = []

  @ViewChild('anexoGrid', { static: false }) anexoGrid: DxDataGridComponent
  public gridData: any
  public listaRemover: any[] = []

  public defaultEye: boolean = false
  public classEye: string = 'fas fa-eye'
  public typeText: string = 'password'
  public toolTipText: string = 'Mostrar senha'
  public adminEqp: string = 'N'
  private alteracao: boolean = false
  public senhaObrigatoria: boolean = true

  constructor(
    private service: UsersService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
    this.permissao('/administracao/usuarios')
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.setFocus(), 1000
    })
  }

  private setFocus(): void {
    if (this.alteracao) {
      document.getElementById('Nome').focus()
    } else {
      document.getElementById('CPF').focus()
    }
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      users: this.formBuilder.group({
        uuid: [''],
        nome: ['', [Validators.required, Validators.maxLength(100)]],
        cpf: ['', [Validators.required, Validators.maxLength(100)]],
        email: [
          '',
          [Validators.required, Validators.maxLength(100), Validators.email],
        ],
        senha: ['', Validators.maxLength(100)],
        status: ['ACTIVE', [Validators.required, Validators.maxLength(100)]],
        limitacaoAcesso: ['N'],
        senhaExpira: ['N'],
        dataExpiracaoSenha: [null],
        adminEquiplano: ['N'],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) {
        this.buscar(uuid)
        this.senhaObrigatoria = false
        this.alteracao = true
      } else {
        this.formulario
          .get('users.senha')
          .setValidators([Validators.required, Validators.maxLength(100)])
        this.loadSelects()
        this.gridData = new DataSource({
          store: [],
          paginate: false,
        })
      }
      this.service
        .getAdminEqp()
        .pipe(first())
        .subscribe(data => {
          this.adminEqp = data.dados
        })
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        if (data.dados.dataExpiracaoSenha)
          data.dados.dataExpiracaoSenha = getMomentDate(
            data.dados.dataExpiracaoSenha,
          )
        this.formulario.get('users').patchValue(data.dados)

        this.gridData = new DataSource({
          store:
            data.dados.anexos && data.dados.anexos.lote
              ? data.dados.anexos.lote
              : [],
          paginate: false,
        })
      })
  }

  public cancelar(): void {
    this.gravarParametros()
    this.router.navigate([`administracao/usuarios`])
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.loading = true
        this.service
          .delete(this.formulario.get('users.uuid').value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Usuário excluído com sucesso.',
              })
              this.cancelar()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Falta preencher os campos obrigatórios',
      })
      this.formulario.markAllAsTouched()
    } else if (
      this.gridBoxValue.length === 0 &&
      !this.formulario.get('users.uuid').value &&
      this.formulario.get('users.adminEquiplano').value === 'N'
    ) {
      this.toastr.send({
        error: true,
        message: 'Selecione pelo menos um grupo de acesso',
      })
    } else {
      this.loading = true
      if (this.formulario.get('users.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    if (this.mensagemErroSenha()) {
      this.service
        .post(this.getUserDto())
        .pipe(
          first(),
          finalize(() => {
            this.loading = false
          }),
        )
        .subscribe(() => {
          this.toastr.send({
            success: true,
            message: 'usuário criado com sucesso.',
          })
          this.cancelar()
        })
    } else {
      this.loading = false
    }
  }

  private atualizar(): void {
    this.service
      .put(this.getUserDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Usuário atualizado com sucesso.',
        })
        this.cancelar()
      })
  }

  private getUserDto(): any {
    const dto = this.formulario.getRawValue()

    dto.users.anexos = { lote: [] }
    this.gridData.items().forEach(item => {
      dto.users.anexos.lote.push({
        ...item,
      })
    })

    if (dto.users.dataExpiracaoSenha)
      dto.users.dataExpiracaoSenha = getParsedDate(dto.users.dataExpiracaoSenha)

    dto.users.anexosExcluir = this.listaRemover

    dto.users.grupoAcesso = this.gridBoxValue

    return dto.users
  }

  private loadSelects(): void {
    this.service
      .getCmbGrupoAcesso()
      .pipe(first())
      .subscribe(data => {
        this.gruposAcesso = data.data
      })
  }

  public mensagemErroSenha(): boolean {
    let erro = true
    const novaSenha = this.formulario.get('users.senha').value

    if (!novaSenha || novaSenha.length < 8) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 8 (oito) caracteres',
      })
      erro = false
    }

    if (!RegExp(/\d/).test(novaSenha)) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 1 (um) número\n',
      })
      erro = false
    }

    if (!RegExp(/[A-Z]/).test(novaSenha)) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 1 (um) carácter maiúsculo',
      })
      erro = false
    }

    if (!RegExp(/[a-z]/).test(novaSenha)) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 1 (um) carácter minúsculo',
      })
      erro = false
    }

    if (!RegExp(/[!@#$%^&*(),.?":{}|<>\-_+]/).test(novaSenha)) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 1 (um) símbolo',
      })
      erro = false
    }

    return erro
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.name === 'addRowButton') {
        item.showText = 'ever'
        item.options.text = 'Novo Anexo'
        item.options.hint = 'Novo Anexo'
        item.options.onClick = () => this.novoRegistro()
      }
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    const lista = []
    this.gridData.items().forEach(item => {
      lista.push(item)
    })
    const dialogRef = this.dialogService.open(AnexoComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno) {
        lista.push(retorno)
        this.gridData = new DataSource({
          store: lista,
          paginate: false,
        })
      }
    })
  }

  public abrir(data): void {
    window.open(data.data.link, '_blank')
  }

  public removerAnexo(e): void {
    this.listaRemover.push(e.data.uuid)
    this.anexoGrid.instance.deleteRow(e.rowIndex)
  }
}
