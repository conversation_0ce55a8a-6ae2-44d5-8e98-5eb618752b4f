import { Injectable } from '@angular/core'
import * as CryptoJS from 'crypto-js'

@Injectable({
  providedIn: 'root',
})
export class CriptService {
  private secretKey: string = 'ThisIsASecretKey'

  private rkEncryptionKey = CryptoJS.enc.Base64.parse(
    'u/Gu5posvwDsXUnV5Zaq4g==',
  )
  private rkEncryptionIv = CryptoJS.enc.Base64.parse('5D9r9ZVzEYYgha93/aUK2w==')

  constructor() {}

  public encryptString(stringToEncrypt: string): string {
    const encrypted = CryptoJS.AES.encrypt(
      stringToEncrypt.toString(),
      this.rkEncryptionKey,
      {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
        iv: this.rkEncryptionIv,
      },
    )
    return encrypted.ciphertext.toString(CryptoJS.enc.Base64)
  }

  public encrypt(stringToEncrypt: string): string {
    const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890,./;'[]=-)(*&^%$#@!~`"
    const lengthOfCode = 40

    let text = ''
    for (let i = 0; i < lengthOfCode; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length))
    }

    const encrypted = CryptoJS.AES.encrypt(
      '{"email":"<EMAIL>","chave":"Equiplano1234@"}'.toString(),
      text,
      {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
        iv: text,
      },
    )
    return encrypted.ciphertext.toString(CryptoJS.enc.Base64)
  }
}
