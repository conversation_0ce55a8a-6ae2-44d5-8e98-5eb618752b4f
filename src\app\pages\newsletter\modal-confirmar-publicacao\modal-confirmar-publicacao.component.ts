import { Component, OnInit } from '@angular/core'
import { NbDialogRef } from '@nebular/theme'

@Component({
  selector: 'eqp-modal-confirmar-publicacao',
  templateUrl: './modal-confirmar-publicacao.component.html',
})
export class ModalConfirmarPublicacaoComponent implements OnInit {
  constructor(protected ref: NbDialogRef<ModalConfirmarPublicacaoComponent>) {}

  public ngOnInit(): void {}

  public cancelar(): void {
    this.ref.close(false)
  }

  public confirmar(): void {
    this.ref.close(true)
  }
}
