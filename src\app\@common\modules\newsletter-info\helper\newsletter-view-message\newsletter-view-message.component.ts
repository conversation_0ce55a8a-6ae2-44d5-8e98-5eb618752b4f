import { Component, Input, OnInit } from '@angular/core'
import { <PERSON><PERSON>anitizer, SafeHtml } from '@angular/platform-browser'
import { NbDialogRef } from '@nebular/theme'
import { NewsletterService } from '@pages/newsletter/newsletter.service'
import { finalize } from 'rxjs/operators'

@Component({
  selector: 'eqp-newsletter-view-message',
  templateUrl: './newsletter-view-message.component.html',
  styleUrls: ['./newsletter-view-message.component.scss'],
})
export class NewsletterViewMessageComponent implements OnInit {
  @Input() boletimUuid: string
  @Input() moduloNome: string
  public loading = false
  public conteudoHtml: SafeHtml

  constructor(
    private newsletterService: NewsletterService,
    private sanitizer: DomSanitizer,
    private dialogRef: NbDialogRef<NewsletterViewMessageComponent>,
  ) {}

  ngOnInit(): void {
    this.carregarBoletim()
  }

  private carregarBoletim() {
    this.loading = true
    this.newsletterService
      .getByUiid(this.boletimUuid)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.conteudoHtml = this.sanitizer.bypassSecurityTrustHtml(
          res.dados.descricao,
        )
      })
  }

  public voltar() {
    this.dialogRef.close()
  }

  public baixar() {
    const element = document.getElementById('conteudo-pdf')

    if (!element) {
      console.error('Elemento não encontrado')
      return
    }

    const conteudo = element.innerHTML

    const printWindow = window.open('', '_blank', 'width=800,height=600')
    if (!printWindow) return

    printWindow.document.open()
    printWindow.document.write(`
    <html>
      <head>
        <title>Boletim</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 20px;
          }
        </style>
      </head>
      <body>
        ${conteudo}
      </body>
    </html>
  `)
    printWindow.document.close()

    printWindow.onload = () => {
      printWindow.focus()
      printWindow.print()
      printWindow.close()
    }
  }
}
