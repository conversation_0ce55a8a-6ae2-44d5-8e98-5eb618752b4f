import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { MenuService } from '@pages/menu.service';
import { finalize, first, map } from 'rxjs/operators';
import { IconeService } from '../services/icon.service';

@Component({
  selector: 'eqp-icone-form',
  templateUrl: './icone-form.component.html',
  styleUrls: ['./icone-form.component.scss'],
})
export class IconeFormComponent
  extends BaseTelasComponent
  implements OnInit  
{

  public readonly pageTitle = 'Ícones';
  public loading: boolean = false;
  public formulario: FormGroup;

  constructor (
    public menuService: MenuService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private service: IconeService,
    private router: Router,
    private toastr: ToastrService,
    private dialogService: NbDialogService,
  ) {
    super(menuService);
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario();
    this.carregarTela();
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      uuid: [''],
      codigo: ['', Validators.required],
      descricao: [''],
    })
  }

  get controls() {
    return this.formulario.controls;
  }

  get iconePreview() {
    const iconCodigo = this.controls['codigo']?.value.trim();
    let preview = '';

    if (!iconCodigo)
      return preview;
    
    if (iconCodigo.includes('fa-')) {
      preview = 'fa ' + iconCodigo;
    } else {
      preview = 'fa fa-' + iconCodigo;
    }

    return preview;
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid'];
      if (uuid) this.buscar(uuid);
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
        map((res) => res.data[0]),
      )
      .subscribe(data => {
        this.formulario.patchValue(data);
      })
  }

  public cancelar(): void {
    this.gravarParametros()
    this.router.navigate([`/equiplano/controle-acesso/icones`])
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .delete(this.controls['uuid']?.value)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Ícone excluído com sucesso.',
              })
              this.cancelar();
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Falta preencher os campos obrigatórios',
      })
      this.formulario.markAllAsTouched()
    } else {
      this.loading = true;
      if (this.controls['uuid']?.value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  
  }

  private novo(): void {
    this.service
      .post(this.tratarDados())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Ícone cadastrado com sucesso.',
        })
        this.cancelar()
      })
  }

  private atualizar(): void {
    this.service
      .put(this.tratarDados())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Ícone atualizado com sucesso.',
        })
        this.cancelar();
      })
  }

  private tratarDados() {

    const dto = this.formulario.getRawValue();
    dto.codigo = dto.codigo.replace('fa ', '');
    if (!dto.codigo.includes('fa-')) {
      dto.codigo = 'fa-'+dto.codigo;
    }

    return dto;
  }

}
