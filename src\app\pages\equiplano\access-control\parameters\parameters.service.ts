import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import {
  ParameterInterface,
  ParameterReturnInterface,
} from '@pages/equiplano/access-control/parameters/parameter'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class ParametersService {
  constructor(private http: HttpClient) {}

  public get(): Observable<ParameterReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ParameterReturnInterface>(`parametro`, {
      headers,
    })
  }

  public getIndividual(uuid: string): Observable<ParameterInterface[]> {
    const headers = new HttpHeaders()

    return this.http.get<ParameterInterface[]>(`parametro/${uuid}`, {
      headers,
    })
  }

  public put(dto: ParameterInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<ParameterInterface>(`parametro/${dto.uuid}`, dto, {
      headers,
      observe: 'response',
    })
  }

  public post(dto: ParameterInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<ParameterInterface>(`parametro`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: ParameterInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<ParameterInterface[]>(`parametro/lote`, batch, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: number): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`parametro/${uuid}`, {
      headers,
    })
  }
}
