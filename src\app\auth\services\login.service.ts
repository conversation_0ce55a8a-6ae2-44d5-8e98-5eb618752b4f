import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'
import { map } from 'rxjs/operators'

import { first } from 'rxjs/operators'
import { CriptService } from '../login/cript.service'
import { LoginInterface } from './../interfaces/login'
import { ResponseDto } from '@core/data/response-dto'

@Injectable({
  providedIn: 'root',
})
export class LoginService {
  private currentUserSubject: BehaviorSubject<any>
  private currentEntitySubject: BehaviorSubject<any>
  public currentUser: Observable<any>
  public currentEntity: Observable<any>

  constructor(private http: HttpClient, private service: CriptService) {
    this.currentUserSubject = new BehaviorSubject<any>(
      JSON.parse(localStorage.getItem('userData'))?.token,
    )
    this.currentEntitySubject = new BehaviorSubject<any>(
      JSON.parse(localStorage.getItem('userData'))?.entidadeUuid,
    )

    this.currentUser = this.currentUserSubject.asObservable()
    this.currentEntity = this.currentEntitySubject.asObservable()
  }

  public get currentUserValue(): any {
    return this.currentUserSubject.value
  }

  public get currentEntityValue(): any {
    return this.currentEntitySubject.value
  }

  public login(payload: LoginInterface): Observable<any> {
    const headers = new HttpHeaders()

    const json = JSON.stringify(payload)

    const dto = {
      chaveAleatoria: this.service.encrypt(json),
      chaveGerada: this.service.encrypt(json),
      chaveHash: this.service.encrypt(json),
      chaveAcesso: this.service.encrypt(json),
      chave: this.service.encrypt(json),
      keyRandom: this.service.encrypt(json),
      keyGenerate: this.service.encrypt(json),
      keyHash: this.service.encrypt(json),
      keyAcess: this.service.encrypt(json),
      key: this.service.encryptString(json),
    }

    return this.http
      .post<any>('auth/login', dto, {
        headers,
        observe: 'response',
      })
      .pipe(
        map((user: any) => {
          if (user) {
            const data = user.body.dados
            localStorage.setItem('userData', JSON.stringify(data))
            this.currentUserSubject.next(data.token)
          }

          return user
        }),
      )
  }

  public loginCertificado(certificado: String): Observable<any> {
    const headers = new HttpHeaders()


    const dto = {
      chaveAleatoria: this.service.encrypt("login123"),
      chaveGerada: this.service.encrypt("login123"),
      chaveHash: this.service.encrypt("login123"),
      chaveAcesso: this.service.encrypt("login123"),
      chave: this.service.encrypt("login123"),
      keyRandom: this.service.encrypt("login123"),
      keyGenerate: this.service.encrypt("login123"),
      keyHash: this.service.encrypt("login123"),
      keyAcess: this.service.encrypt("login123"),
      key: certificado,
    }

    return this.http
      .post<any>('auth/login-certificado', dto, {
        headers,
        observe: 'response',
      })
      .pipe(
        map((user: any) => {
          if (user) {
            const data = user.body.dados
            localStorage.setItem('userData', JSON.stringify(data))
            this.currentUserSubject.next(data.token)
          }

          return user
        }),
      )
  }

  
    public getCertificado(): any {

      return new Observable(x => {
        const request = new XMLHttpRequest()
        request.open(
          'get',
          `https://conta.equiplano.com.br:8443/certificadoBackend/certificado`,
          true,
        )
        request.send()
        request.onload = function () {
          const data = this.response
          x.next(data)
        }
        request.onerror = function () {
          x.next(null)
        }
      })
    }
  

  public getMunicipios(): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>('auth/municipio', {
      headers,
    })
  }

  public entidades(): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>('auth/entidade', {
      headers,
    })
  }

  public exercicios(): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>('auth/exercicio', {
      headers,
    })
  }

  public atualizarToken(tokenRequest: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<any>('auth/token', tokenRequest, {
      headers,
    })
  }

  public cliente(clienteUuid): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`auth/cliente/${clienteUuid}`, {
      headers,
    })
  }

  public logout(): void {
    const idToken = JSON.parse(localStorage.getItem('userData'))?.idToken
    localStorage.removeItem('userData')
    if (idToken)
      this.removerSessao(idToken)
        .pipe(first())
        .subscribe(data => {})
    this.currentUserSubject.next(null)
  }

  public gravarSessao(sessao: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<any>(`auth/sessao`, sessao, {
      headers,
    })
  }

  public recuperaSessao(idToken: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`auth/sessao/${idToken}`, {
      headers,
    })
  }

  public removerSessao(idToken: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`auth/sessao/${idToken}`, {
      headers,
    })
  }

  public putSenha(dto: any): Observable<any> {
    const json = JSON.stringify(dto)

    const key = {
      chaveAleatoria: this.service.encrypt(json),
      chaveGerada: this.service.encrypt(json),
      chaveHash: this.service.encrypt(json),
      chaveAcesso: this.service.encrypt(json),
      chave: this.service.encrypt(json),
      keyRandom: this.service.encrypt(json),
      keyGenerate: this.service.encrypt(json),
      keyHash: this.service.encrypt(json),
      keyAcess: this.service.encrypt(json),
      key: this.service.encryptString(json),
    }

    const headers = new HttpHeaders()

    return this.http.put<any>(`usuario/senha`, key, {
      headers,
      observe: 'response',
    })
  }

  public esqueciSenha(usuario: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<any>(
      `auth/esqueci-senha/${usuario}`,
      {},
      {
        headers,
        observe: 'response',
      },
    )
  }

  public resetarSenha(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<any>(
      `auth/nova-senha/${uuid}`,
      {},
      {
        headers,
        observe: 'response',
      },
    )
  }

  public isUsuarioEquiplano() {
    const headers = new HttpHeaders()
    return this.http.get<ResponseDto<'S' | 'N'>>('usuario/admin-equiplano', {
      headers, 
    }).pipe(map(data => data.dados === 'S'))
  }
}
