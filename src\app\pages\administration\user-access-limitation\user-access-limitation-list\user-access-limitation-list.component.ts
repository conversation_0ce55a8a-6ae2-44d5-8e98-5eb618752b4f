import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalConfirmarComponent } from '@common/dialogs/modal-confirmar/modal-confirmar.component';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { ToastrService } from '@common/services/toastr/toastr.service';
import { NbDialogService } from '@nebular/theme';
import { UsersService } from '@pages/administration/users/users.service';
import { MenuService } from '@pages/menu.service';
import DataSource from 'devextreme/data/data_source';
import { Subscription } from 'rxjs';
import { first } from 'rxjs/operators';

@Component({
  selector: 'eqp-user-access-limitation-list',
  templateUrl: './user-access-limitation-list.component.html',
  styleUrls: ['./user-access-limitation-list.component.scss']
})
export class UserAccessLimitationListComponent extends BaseTelasComponent implements OnInit, OnDestroy {

  public loading: boolean = false
  public pageTitle: string = 'Limitação de Acesso'

  public gridData: any

  private subscription: Subscription
  
  constructor(
    private service: UsersService,
    private toastr: ToastrService,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
    this.permissao('/administracao/limitacao-acesso')
  }

  public ngOnInit(): void {
    this.fetchGrid();
  }

  public ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe()
  }
  
  fetchGrid() {
    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltro('uuid', 'limitacao_acesso_orgao/paginado', 10),
      paginate: true,
      pageSize: 10,
      map: item => {
        return {
          ...item,
          nome: item?.usuario?.nome,
          cpf: this.applyMask(item?.usuario?.cpf),
          orgao: item.orgao.codigo + ' - ' + item.orgao.nome,
          unidade: item?.unidade?.nome,
        }
      }
    })
  }
  
  applyMask(value: string): string {
    const cleaned = ('' + value).replace(/\D/g, '');
    let mask = '';
    let result = '';
  
    if (cleaned.length <= 11) {
      mask = '000.000.000-00';
    } else if (cleaned.length === 14) {
      mask = '00.000.000/0000-00';
    }
  
    let count = 0;
    for (let i = 0; i < mask.length; i++) {
      if (mask[i] === '0') {
        if (count < cleaned.length) {
          result += cleaned[count];
          count++;
        }
      } else {
        result += mask[i];
      }
    }
    return result;
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.name === 'addRowButton') {
        item.showText = 'ever'
        item.options.text = 'Novo'
        item.options.hint = 'Novo'
        item.options.onClick = () => this.novoRegistro()
      }

      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public novoRegistro(): void {
    this.gravarParametros()
    this.router.navigate([`administracao/limitacao-acesso/novo`])
  }

  public alterar(uuid: string): void {
    this.gravarParametros()
    this.router.navigate([`administracao/limitacao-acesso/edit/${uuid}`])
  }

  public remover(uuid: string): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.service
          .deleteUserAceessLimitation(uuid)
          .pipe(first())
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Limitação excluída com sucesso.',
              })
             this.fetchGrid()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }
}
