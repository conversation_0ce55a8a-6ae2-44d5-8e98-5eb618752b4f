import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { CrudService } from '@common/services/crud.service'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import DataSource from 'devextreme/data/data_source'
import { NewsletterService } from '../newsletter.service'

@Component({
  selector: 'eqp-newsletter-list',
  templateUrl: './newsletter-list.component.html',
  styleUrls: ['./newsletter-list.component.scss'],
})
export class NewsletterListComponent
  extends BaseTelasComponent
  implements OnInit
{
  public loading = false
  public pageTitle = 'Equiplano | Boletim Informativo'
  public modulos: DataSource

  private readonly routeBase = 'boletim-informativo'

  constructor(
    private _router: Router,
    private _service: NewsletterService,
    private _dialogService: NbDialogService,
    private _toastr: ToastrService,
    public menuService: MenuService,
    private _crudService: CrudService
  ) {
    super(menuService)
  }

  public ngOnInit(): void {
    this.setGridData()
  }

  public setGridData() {
    this.modulos = new DataSource({
      store: this._crudService.getDataSourceFiltro(
        'uuid',
        'boletim-informativo/modulos',
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public onToolbarPreparing(event: any): void {
    event.toolbarOptions.items[0].showText = 'ever'
    event.toolbarOptions.items[0].options.text = 'Novo boletim'
    event.toolbarOptions.items[0].options.hint = 'Novo boletim'

    event.toolbarOptions.items.forEach((item: any, index) => {
      if (item.options) {
        item.options.elementAttr = {
          id: 'action' + index,
        }
      } else {
        item['options'] = {
          elementAttr: {
            id: 'action' + index,
          },
        }
      }
    })
  }

  public newRegistration(): void {
    this.gravarParametros()
    this._router.navigate([`${this.routeBase}/novo`])
  }

}
