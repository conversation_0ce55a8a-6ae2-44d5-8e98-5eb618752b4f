import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import { NbCheckboxModule, NbInputModule } from '@nebular/theme'
import { DxColorBoxModule, DxSelectBoxModule } from 'devextreme-angular'

import { ClientsComponent } from './clients/client/clients.component'
import {
  ClientsListComponent,
} from './clients/clients-list/clients-list.component'
import { EquiplanoRoutingModule } from './equiplano-routing.module'
import { EquiplanoComponent } from './equiplano.component'

@NgModule({
  declarations: [EquiplanoComponent, ClientsComponent, ClientsListComponent],
  imports: [
    CommonModule,
    EquiplanoRoutingModule,
    CommonToolsModule,
    DxColorBoxModule,
    NbInputModule,
    DxSelectBoxModule,
    NbCheckboxModule,
  ],
})
export class EquiplanoModule {}
