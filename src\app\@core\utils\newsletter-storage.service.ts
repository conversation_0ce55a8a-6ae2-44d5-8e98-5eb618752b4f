import { Injectable } from '@angular/core';
import { NewsletterInfoService } from '@common/modules/newsletter-info/services/newsletter-info.service';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NewsletterStorageService {
  private totalNewsletters = new BehaviorSubject<number>(0)

  public readonly totalNewsletters$ = this.totalNewsletters.asObservable()

  constructor(
    private newsletterInfoService: NewsletterInfoService
  ) {
    this.init()
  }

  public totalNewslettersValue() {
    return this.totalNewsletters.value
  }

  public setTotalNewsletters(totalNewsletters: number) {
    console.log(totalNewsletters)
    this.totalNewsletters.next(totalNewsletters)
  }

  public restore() {
    this.newsletterInfoService.listaPublicacao().subscribe((_) => {
      this.init()
    })
  }

  private init() {
    this.newsletterInfoService.loadTotalNewsletters()
    .subscribe((res) => {
      this.setTotalNewsletters(res.dados)
    })
  }
}
