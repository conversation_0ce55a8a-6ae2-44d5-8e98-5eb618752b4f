import { Injectable } from '@angular/core';
import { NewsletterService } from '@pages/newsletter/newsletter.service';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NewsletterStorageService {
  private totalNewsletters = new BehaviorSubject<number>(0)

  public readonly totalNewsletters$ = this.totalNewsletters.asObservable()

  constructor(
    private newsletterService: NewsletterService
  ) {
    this.init()
  }

  public totalNewslettersValue() {
    return this.totalNewsletters.value
  }

  public setTotalNewsletters(totalNewsletters: number) {
    console.log(totalNewsletters)
    this.totalNewsletters.next(totalNewsletters)
  }

  public restore() {
    this.newsletterService.listaPublicacao().subscribe((_) => {
      this.init()
    })
  }

  private init() {
    this.newsletterService.loadTotalNewsletters()
    .subscribe((res) => {
      this.setTotalNewsletters(res.dados)
    })
  }
}
