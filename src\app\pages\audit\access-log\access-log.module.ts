import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AccessLogRoutingModule } from './access-log-routing.module';
import { AccessLogComponent } from './access-log.component';
import { AccessLogSearchComponent } from './access-log-search/access-log-search.component';
import { CommonToolsModule } from '@common/common-tools.module';
import { DetailAccessLogComponent } from './detail-access-log/detail-access-log.component';
import { DefaultSearchDialogModule } from '@pages/shared/searchs/default-search-dialog/default-search-dialog.module';


@NgModule({
  declarations: [AccessLogComponent, AccessLogSearchComponent, DetailAccessLogComponent],
  imports: [
    CommonModule,
    CommonToolsModule,
    DefaultSearchDialogModule,
    AccessLogRoutingModule
  ]
})
export class AccessLogModule { }
