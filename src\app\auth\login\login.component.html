<nb-layout>
    <nb-layout-column>
    <div class="login">
      <div class="left-panel" *ngIf="!authenticated && !municipioSelected && !entitySelected">
        <img src="assets/img/bannerHome-134.png" class="bg-image">
      </div>
      
      <div class="right-panel" [ngClass]="{'full-width': authenticated || municipioSelected || entitySelected}">
        <!-- Formulário de login inicial -->
        <div class="login-content" *ngIf="!authenticated && !municipioSelected && !entitySelected">
          <div class="logo">
            <img src="assets/img/name-white-logo.png" alt="Logo">
          </div>
          <p class="login-subtitle">Informe seus dados para acessar a sua conta</p>
          
          <form [formGroup]="loginForm">
            <div class="form-group">
              <label>Usuário</label>
              <input 
                type="text" 
                formControlName="username" 
                placeholder="Informe seu usuário"
                class="form-control"
              >
            </div>
            
            <div class="form-group">
              <label>Senha</label>
              <div class="password-container">
                <input 
                  [type]="typeText" 
                  formControlName="password" 
                  placeholder="Informe sua senha"
                  class="form-control"
                >
                <span class="password-toggle" (click)="toggleDefault()" [title]="toolTipText">
                  <i [class]="classEye"></i>
                </span>
              </div>
              <a (click)="esqueciSenha()" class="forgot-link">Esqueci a senha</a>
            </div>
            
            <button 
              type="button" 
              class="btn-confirm mt-2" 
              [disabled]="loading || !f.username.value || !f.password.value"
              (click)="onSubmit()"
            >
              Confirmar
            </button>
            <button 
              type="button" 
              class="btn-certificate" 
              [disabled]="loading"
              (click)="loginCertificado()"
            >
              <i class="fas fa-id-card"></i> Login com Certificado Digital
            </button>
          </form>
        </div>
        
        <!-- Seleção de cliente -->
        <div class="selection-content" *ngIf="authenticated">
          <div class="selection-header">
            <h3>Selecionar cliente</h3>
            <button class="btn-back" (click)="back()">
              <i class="fas fa-undo-alt"></i> Voltar ao login
            </button>
          </div>
          
          <div class="custom-search-container">
            <div class="search-icon">
              <i class="fas fa-search"></i>
            </div>
            <input
              type="text"
              class="custom-search-input"
              placeholder="Pesquisar cliente..."
              [formControl]="searchMunicipioControl"
            />
            <div class="clear-icon" *ngIf="searchMunicipioControl.value" (click)="clearMunicipioSearch()">
              <i class="fas fa-times"></i>
            </div>
          </div>
          
          <div class="selection-list">
            <div 
              *ngFor="let item of filteredMunicipios" 
              class="selection-item"
              (click)="selectMunicipio(item)"
            >
              <div class="item-image">
                <i class="fas fa-city"></i>
              </div>
              <div class="item-info">
                <h4>{{ item.nome }}</h4>
              </div>
            </div>
            
            <div class="no-results" *ngIf="filteredMunicipios.length === 0">
              <p>Nenhum cliente correspondente a pesquisa encontrado.</p>
            </div>
          </div>
        </div>
        
        <!-- Seleção de entidade -->
        <div class="selection-content" *ngIf="municipioSelected">
          <div class="selection-header">
            <h3>Selecionar entidade</h3>
            <button class="btn-back" (click)="back()">
              <i class="fas fa-undo-alt"></i> Voltar ao login
            </button>
          </div>
          
          <div class="custom-search-container">
            <div class="search-icon">
              <i class="fas fa-search"></i>
            </div>
            <input
              type="text"
              class="custom-search-input"
              placeholder="Pesquisar entidade..."
              [formControl]="searchEntityControl"
            />
            <div class="clear-icon" *ngIf="searchEntityControl.value" (click)="clearEntitySearch()">
              <i class="fas fa-times"></i>
            </div>
          </div>
          
          <div class="selection-list">
            <div 
              *ngFor="let item of filteredEntities" 
              class="selection-item"
              (click)="selectEntity(item)"
            >
              <div class="item-image">
                <img [src]="item.brasao ? item.brasao.link : 'assets/img/equiplano-logo-vertical.png'" alt="Brasão">
              </div>
              <div class="item-info">
                <h4>{{ item.codigo ? item.codigo + ' - ' : '' }}{{ item.nome }}</h4>
              </div>
            </div>
            
            <div class="no-results" *ngIf="filteredEntities.length === 0">
              <p>Nenhuma entidade correspondente a pesquisa encontrada.</p>
            </div>
          </div>
        </div>
        
        <!-- Seleção de exercício -->
        <div class="selection-content" [formGroup]="loginForm" *ngIf="entitySelected">
          <div class="selection-header">
            <h3>Selecionar exercício</h3>
            <button class="btn-back" (click)="back()">
              <i class="fas fa-undo-alt"></i> Voltar à seleção de entidades
            </button>
          </div>
          
          <div class="d-flex">
            <div class="entity-info">
              <div class="item-image">
                <img [src]="entity?.brasao ? entity.brasao.link : 'assets/img/equiplano-logo-vertical.png'" alt="Brasão">
              </div>
              <h4>{{entity.codigo}} - {{ entity.nome }}</h4>
            </div>
            
            <div class="exercise-selection">
              <div class="form-group">
                <eqp-nebular-select
                  [selectValues]="exercises"
                  [hasFullWidth]="true"
                  [size]="'small'"
                  formControlName="exercise"
                  name="exercise"
                  label="Exercício"
                >
                </eqp-nebular-select>
              </div>
              
              <button 
                type="button" 
                class="btn-next" 
                [disabled]="!f.exercise.value"
                (click)="selectExercise()"
              >
                Confirmar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nb-layout-column>
</nb-layout>
