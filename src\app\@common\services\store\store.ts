import { Observable, Subject } from 'rxjs';
import { distinctUntilChanged, map, share, takeUntil } from 'rxjs/operators';
import { StateSubject } from './state-subject';

export class Store<T> {
  private _unsubscriber$: Subject<void> = new Subject<void>();
  private _state$!: StateSubject<T>;

  constructor(value: T, unsubscriber: Subject<void>) {
    this._state$ = new StateSubject(value);
    this._unsubscriber$ = unsubscriber;
  }

  updateStore(value: T){
    const valueBefore = {...this._state$.value};
    this._state$.update(value);
    this._state$.next({...valueBefore});
  }

  setState(value: Partial<T>): void {
    this._state$.next({ ...this._state$.value, ...value });
  }

  getState(): T {
    return this._state$.value;
  }

  select<K extends keyof T>(key: K): Observable<T[K]> {
    return this._state$.value$.pipe(
      map((state) => state[key]),
      distinctUntilChanged((a, b) => Object.is(a, b)),
      share(),
      takeUntil(this._unsubscriber$),
    );
  }

  selectAll(): Observable<T> {
    return this._state$.value$.pipe(share(), takeUntil(this._unsubscriber$));
  }

  reset(): void {
    this._state$.reset();
  }
}
