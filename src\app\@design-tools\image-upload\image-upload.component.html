<label
  *ngIf="!imageChangedEvent"
  class="image-upload-container btn btn-bwm"
>
  <span>Select Image</span>
  <input
    type="file"
    accept="image/*"
    (change)="processFile($event)"
  >
</label>

<div class="row mb-2">
  <div class="col-md-12">
    <image-cropper
      *ngIf="imageChangedEvent"
      [imageChangedEvent]="imageChangedEvent"
      [maintainAspectRatio]="true"
      [resizeToWidth]="950"
      [aspectRatio]="4 / 4"
      format="png "
      (imageCroppedFile)="imageCropped($event)"
      (imageLoaded)="imageLoaded()"
    ></image-cropper>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <eqp-nebular-button
      [buttonVisible]="imageChangedEvent"
      [buttonType]="'primary'"
      [buttonTitle]="'Concluir upload'"
      [buttonText]="'Concluir'"
      [buttonIcon]="'fas fa-cloud-upload-alt'"
      [buttonIconVisible]="true"
      [buttonClass]="'float-right'"
      (buttonEmitter)="uploadImage()"
      buttonId="upload-file"
    >
    </eqp-nebular-button>

    <eqp-nebular-button
      [buttonVisible]="imageChangedEvent"
      [buttonType]="'danger'"
      [buttonTitle]="'Cancelar recorte'"
      [buttonText]="'Cancelar'"
      [buttonIcon]="'fas fa-undo-alt'"
      [buttonIconVisible]="true"
      [buttonClass]="'float-right mr-2'"
      (buttonEmitter)="cancelCropping()"
      buttonId="cancel-upload-file"
    >
    </eqp-nebular-button>
  </div>
</div>

<div
  *ngIf="selectedFile && selectedFile.src"
  class="img-preview-container"
>
  <div
    class="img-preview"
    [ngStyle]="{'background-image': 'url(' + selectedFile.src + ')'}"
  >
  </div>

  <div
    *ngIf="selectedFile.pending"
    class="img-loading-overlay"
  >
    <div class="img-spinning-circle"></div>
  </div>

  <div
    *ngIf="selectedFile.status === 'OK'"
    class="alert alert-success"
  > Image Uploaded Sucesfuly! </div>
  <div
    *ngIf="selectedFile.status === 'FAIL'"
    class="alert alert-danger"
  > Image Upload Failed </div>
</div>
