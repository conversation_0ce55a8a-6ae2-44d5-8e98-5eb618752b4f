import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class OperationsService {
  private baseUri = "operacoes"
  constructor(protected httpClient: HttpClient) { }

  public getDataByFilters(dto: any) {
    let params = new HttpParams()

    Object.keys(dto).forEach((item) => {
      const value = dto[item]

      if(value) params = params.append(item, value)
    })

    return this.httpClient.get<any>(`${this.baseUri}/pesquisa`, {params: params})
  }
}
