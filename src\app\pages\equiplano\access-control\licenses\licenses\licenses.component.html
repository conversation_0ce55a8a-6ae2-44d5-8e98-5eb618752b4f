<nb-card [nbSpinner]="loading" [nbSpinnerStatus]="'info'">
  <nb-card-header>
    <div class="row">
      <div class="col-md-8">
        <h5>{{ pageTitle }}</h5>
      </div>
    </div>
    <div class="mt-2">
      <eqp-breadcrumb></eqp-breadcrumb>
    </div>
  </nb-card-header>
  <nb-card-body [formGroup]="formulario">
    <div class="row" formGroupName="licenca">
      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-input
          [style]="'date'"
          [size]="'small'"
          [shape]="'rectangle'"
          formControlName="expiraEm"
          name="Data de expiração"
          label="Data de expiração"
          placeholder="Data de expiração"
          required="true"
          errorMessage="É obrigatório preencher a data de expiração"
        >
        </eqp-nebular-input>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Módulo"
          label="Módulo"
          placeholder="Módulo"
          formControlName="moduloUuid"
          [dataSource]="modules"
          valueExpr="uuid"
          displayExpr="descricao"
          required="true"
        ></eqp-nebular-select>
      </div>

      <div class="col-md-4 col-sm-12 mb-4">
        <eqp-nebular-select
          [size]="'small'"
          [shape]="'rectangle'"
          name="Cliente"
          label="Cliente"
          placeholder="Cliente"
          formControlName="clienteUuid"
          [dataSource]="clients"
          valueExpr="uuid"
          displayExpr="nome"
          required="true"
        ></eqp-nebular-select>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="row">
      <div class="col-md-12">
        <button type="button" class="btn btn-dark" (click)="cancelar()">
          Voltar
        </button>
        <button
          *ngIf="formulario.get('licenca.uuid').value"
          type="button"
          class="btn btn-danger ml-3 float-md-right"
          (click)="remover()"
        >
          Apagar
        </button>
        <button
          type="button"
          class="btn btn-success float-md-right"
          (click)="gravar()"
        >
          Gravar
        </button>
      </div>
    </div>
  </nb-card-footer>
</nb-card>
