export interface PermissionInterface {
  uuid?: number | string
  grupoAcessoUuid?: string | number
  acaoUuid?: string
  nivelAcesso?: string | 'GUEST' | 'VIEWER' | 'EDITOR' | 'FULL'
  ordem?: number
  status?: string | 'ACTIVE' | 'INACTIVE'
  criadoEm?: string
  atualizadoEm?: string
}

export interface AccessGroupInterface {
  uuid?: number | string
  nome?: string
  descricao?: string
  clienteUuid?: string
  moduloUuid?: string
  nivelAcesso?: string
  status?: string | 'ACTIVE' | 'INACTIVE'
  criadoEm?: string
  atualizadoEm?: string
}

export interface AccessGroupReturnInterface {
  dados: AccessGroupInterface[]
  success: boolean
}
