import { Component, ElementRef, EventEmitter, HostListener, OnD<PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { debounceTime, filter, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'eqp-input-search-header',
  templateUrl: './input-search-header.component.html',
  styleUrls: ['./input-search-header.component.scss'],
})
export class InputSearchHeaderComponent implements OnInit, OnDestroy {
  @ViewChild('searchInput') input!: ElementRef<HTMLInputElement>;
  @ViewChild('textMirror') textMirror!: ElementRef;
  public inputWidth = 250; // largura mínima
  public inputValue = '';
  private readonly MINIMUM_WIDTH: number = 250
  private readonly DEBOUNCE_TIME: number = 1000;
  private destroy$ = new Subject<void>();
  private inputChanged$ = new Subject<string>();
  @Output() searchEnter = new EventEmitter<string>();

  @HostListener('document:keydown.escape', ['$event'])
  handleEscapeKey(event: KeyboardEvent) {
    this.resetInputWidth();
    this.inputValue = '';
  }

  constructor(private _router: Router) {
    this._router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationEnd) => {
        if (!event.urlAfterRedirects.startsWith('/outros/busca/')) {
          this.resetInputWidth();
          this.inputValue = '';
        }
      })
  }

  ngOnInit(): void {
    this.loadInputListener();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadInputListener() {
    this.inputChanged$
    .pipe(
      debounceTime(this.DEBOUNCE_TIME),
      takeUntil(this.destroy$)
    )
    .subscribe((term) => {
      const trimmed = term.trim();
      if (trimmed.length > 0) {
        this.searchEnter.emit(trimmed);
      }
    });
  }

  public updateInputWidth(): void {
    const width = this.textMirror?.nativeElement?.offsetWidth || 0;
    this.inputWidth = Math.max(width, this.MINIMUM_WIDTH);
  }

  public onInputChange(): void {
    this.updateInputWidth();
    this.inputChanged$.next(this.inputValue);
  }

  private resetInputWidth(): void {
    this.inputWidth = this.MINIMUM_WIDTH;
  }
}
