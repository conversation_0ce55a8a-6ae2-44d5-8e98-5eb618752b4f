{"items": [{"key": "$base-border-color", "value": "#e4e9f2"}, {"key": "$base-accent", "value": "#007bff"}, {"key": "$base-border-radius", "value": "0.25rem"}, {"key": "$texteditor-bg", "value": "rgba(247, 249, 252, 1)"}, {"key": "$texteditor-focused-bg", "value": "rgba(255, 255, 255, 1)"}, {"key": "$texteditor-hover-bg", "value": "rgba(237, 241, 247, 1)"}, {"key": "$texteditor-border-color", "value": "rgba(228, 233, 242, 1)"}, {"key": "$texteditor-hover-border-color", "value": "rgba(228, 233, 242, 1)"}, {"key": "$texteditor-focused-border-color", "value": "#3366ff"}], "baseTheme": "material.blue.light.compact", "outputColorScheme": "custom-scheme", "makeSwatch": false, "version": "21.2.7", "widgets": [], "removeExternalResources": false}