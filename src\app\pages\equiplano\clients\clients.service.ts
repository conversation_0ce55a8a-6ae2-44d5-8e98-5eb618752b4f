import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import {
  ClientInterface,
  ClientReturnInterface,
} from '@pages/equiplano/clients/clients'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class ClientsService extends CrudService {
  constructor(private http: HttpClient) {
    super(http)
  }

  public get(): Observable<ClientReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ClientReturnInterface>('cliente', {
      headers,
    })
  }

  public getIndividual(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`cliente/${uuid}`, {
      headers,
    })
  }

  public put(dto: ClientInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<ClientInterface>(`cliente/${dto.uuid}`, dto, {
      headers,
      observe: 'response',
    })
  }

  public post(dto: ClientInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<ClientInterface>('cliente', dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: ClientInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<ClientInterface[]>('cliente/lote', batch, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: number | string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`cliente/${uuid}`, {
      headers,
    })
  }
}
