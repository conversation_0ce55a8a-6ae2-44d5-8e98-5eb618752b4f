import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { LoginService } from '../services/login.service';

@Injectable({
  providedIn: 'root'
})
export class BoletimInformativoGuard implements CanActivate {
  constructor(
    private loginService: LoginService, 
    private router: Router,
    ) {
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
      this.loginService.isUsuarioEquiplano().subscribe(isUsuarioEquiplano => {
        if(isUsuarioEquiplano) return true
        else {
          this.router.navigate(['boletim-informativo-visualizar'])
          return false
        }
      })
    return true;
  }
  
}
