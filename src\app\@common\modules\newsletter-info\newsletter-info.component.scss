/* Importar estilos compartilhados de pesquisa */
@import '/src/app/@common/modules/newsletter-info/styles/search-input.scss';

.search-container {
  max-width: 600px;
  margin: 0 auto 1.5rem auto;
}

.no-modules-found {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 2rem;
}

.no-modules-content {
  text-align: center;
  max-width: 400px;

  i {
    font-size: 3rem;
    color: var(--text-hint-color);
    margin-bottom: 1rem;
  }

  h4 {
    color: var(--text-basic-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
  }

  p {
    color: var(--text-hint-color);
    margin-bottom: 1.5rem;
    line-height: 1.5;
  }

  .btn {
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;

    &.btn-outline-primary {
      border-color: #007bff;
      color: #007bff;

      &:hover {
        background-color: #007bff;
        color: white;
      }
    }
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .search-container {
    margin: 0 0 1rem 0;
  }

  .no-modules-found {
    min-height: 200px;
    padding: 1rem;
  }

  .no-modules-content {
    i {
      font-size: 2rem;
    }

    h4 {
      font-size: 1.25rem;
    }

    p {
      font-size: 0.9rem;
    }
  }
}