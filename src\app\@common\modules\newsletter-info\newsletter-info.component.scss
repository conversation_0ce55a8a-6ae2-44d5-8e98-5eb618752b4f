/* Estilos do campo de pesquisa */
.search-container {
  max-width: 600px;
  margin: 0 auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 12px 45px 12px 40px;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 16px;
  background: var(--background-basic-color-1);
  color: var(--text-basic-color);
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }

  &::placeholder {
    color: var(--text-hint-color);
    font-style: italic;
  }
}

.search-icon {
  position: absolute;
  left: 15px;
  color: var(--text-hint-color);
  font-size: 16px;
  pointer-events: none;
}

.clear-search-btn {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--text-hint-color);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: var(--background-basic-color-2);
    color: var(--text-basic-color);
  }

  i {
    font-size: 14px;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .search-container {
    margin: 0;
  }

  .search-input {
    font-size: 14px;
    padding: 10px 40px 10px 35px;
  }

  .search-icon {
    left: 12px;
    font-size: 14px;
  }

  .clear-search-btn {
    right: 10px;
    padding: 6px;
  }
}