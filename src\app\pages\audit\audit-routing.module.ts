import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { AuditComponent } from './audit.component'
import { OperationsComponent } from './operations/operations-report.component'
import { AccessLogComponent } from './access-log/access-log.component'

const routes: Routes = [
  {
    path: '',
    component: AuditComponent,
    children: [
      {
        path: 'operacoes',
        loadChildren: () =>
          import('./operations/operations.module').then(
            m => m.OperationsModule,
          ),
      },
      {
        path: 'log-acesso',
        loadChildren: () =>
          import('./access-log/access-log.module').then(
            m => m.AccessLogModule,
          ),
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AuditRoutingModule {}
