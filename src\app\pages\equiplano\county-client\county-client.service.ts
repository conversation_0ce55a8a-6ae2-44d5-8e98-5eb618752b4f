import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class CountyClientService extends CrudService {
  constructor(private http: HttpClient) {
    super(http)
  }

  public getIndividual(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`municipio_cliente/${uuid}`, {
      headers,
    })
  }

  public put(dto: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<any>(`municipio_cliente/${dto.uuid}`, dto, {
      headers,
      observe: 'response',
    })
  }

  public post(dto: any): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<any>(`municipio_cliente`, dto, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`municipio_cliente/${uuid}`, {
      headers,
    })
  }
}
