{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"eqp-central": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": true, "preserveSymlinks": true, "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico", "src/favicon.png", "src/manifest.webmanifest"], "styles": ["node_modules/devextreme/dist/css/dx.common.css", "node_modules/bootstrap/dist/css/bootstrap.css", "node_modules/typeface-exo/index.css", "node_modules/roboto-fontface/css/roboto/roboto-fontface.css", "node_modules/ionicons/scss/ionicons.scss", "node_modules/@fortawesome/fontawesome-free/css/all.css", "node_modules/socicon/css/socicon.css", "node_modules/nebular-icons/scss/nebular-icons.scss", "node_modules/pace-js/templates/pace-theme-flash.tmpl.css", "src/app/@theme/styles/styles.scss"], "scripts": ["node_modules/pace-js/pace.min.js", "node_modules/tinymce/tinymce.min.js", "node_modules/tinymce/themes/modern/theme.min.js", "node_modules/tinymce/plugins/link/plugin.min.js", "node_modules/tinymce/plugins/paste/plugin.min.js", "node_modules/tinymce/plugins/table/plugin.min.js"], "allowedCommonJsDependencies": ["lodash", "devextreme", "moment-es6", "devextreme/localization", "angular2-text-mask"], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "configurations": {"treinamento": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "15kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.treinamento.ts"}]}, "apresentacao": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "15kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.apresentacao.ts"}]}, "dev": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "15kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "homolog": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "15kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.homolog.ts"}]}, "production": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "15kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "eqp-central:build", "port": 4201}, "configurations": {"production": {"browserTarget": "eqp-central:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "eqp-central:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["node_modules/pace-js/pace.min.js", "node_modules/tinymce/tinymce.min.js", "node_modules/tinymce/themes/modern/theme.min.js", "node_modules/tinymce/plugins/link/plugin.min.js", "node_modules/tinymce/plugins/paste/plugin.min.js", "node_modules/tinymce/plugins/table/plugin.min.js"], "styles": ["node_modules/bootstrap/dist/css/bootstrap.css", "node_modules/typeface-exo/index.css", "node_modules/roboto-fontface/css/roboto/roboto-fontface.css", "node_modules/ionicons/scss/ionicons.scss", "node_modules/font-awesome/scss/font-awesome.scss", "node_modules/socicon/css/socicon.css", "node_modules/nebular-icons/scss/nebular-icons.scss", "node_modules/pace-js/templates/pace-theme-flash.tmpl.css", "src/app/@theme/styles/styles.scss"], "assets": ["src/assets", "src/favicon.ico", "src/favicon.png", "src/manifest.webmanifest"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "typeCheck": true, "exclude": []}}}}, "eqp-central-e2e": {"root": "", "sourceRoot": "", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "eqp-central:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": []}}}}}, "defaultProject": "eqp-central", "schematics": {"@schematics/angular:component": {"prefix": "eqp", "style": "scss"}, "@schematics/angular:directive": {"prefix": "eqp"}}}