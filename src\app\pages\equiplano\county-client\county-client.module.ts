import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'
import { NbInputModule } from '@nebular/theme'

import { CountyClientListComponent } from './county-client-list/county-client-list.component'
import { CountyClientRoutingModule } from './county-client-routing.module'
import { CountyClientComponent } from './county-client/county-client.component'

@NgModule({
  declarations: [CountyClientListComponent, CountyClientComponent],
  imports: [
    CommonModule,
    CountyClientRoutingModule,
    CommonToolsModule,
    NbInputModule,
  ],
})
export class CountyClientModule {}
