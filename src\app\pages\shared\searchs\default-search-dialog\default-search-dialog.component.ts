import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { BaseTelasComponent } from '@common/misc/base-telas.component';
import { CrudService } from '@common/services/crud.service';
import { NbDialogRef } from '@nebular/theme';
import { MenuService } from '@pages/menu.service';
import { DefaultSearchDataInterface } from '@pages/shared/interfaces/default-search-data';
import { DxDataGridComponent } from 'devextreme-angular';
import DataSource from 'devextreme/data/data_source';

@Component({
  selector: 'eqp-default-search-dialog',
  templateUrl: './default-search-dialog.component.html',
  styleUrls: ['./default-search-dialog.component.scss']
})
export class DefaultSearchDialogComponent extends BaseTelasComponent
implements OnInit {
  @Input() searchData: DefaultSearchDataInterface = {
    uri: 'uri_padrao',
    dialogTitle: 'Título padrão',
    columns: [{ caption: 'Nome', dataField: 'nome' }],
  }
  @Input() searchPanelVisible = true
  @Input() dataSourceLookup: any[]
  @Input() objIsData = true

  dataSource: DataSource
  dataSourceArray: any[]
  public grid: DxDataGridComponent
  selectedRowKeys: string[] = []
  loading: boolean = false

  constructor(
    public router: Router,
    public menuService: MenuService,
    private crudService: CrudService,
    private dialogRef: NbDialogRef<DefaultSearchDialogComponent>,
  ) {
    super(menuService)
    this.permissao(this.searchData.uri)
  }

  ngOnInit(): void {
    this.fetchGrid()
  }

  fetchGrid() {
    this.loading = true
    if (this.objIsData) {
      this.dataSource = new DataSource({
        store: this.crudService.getDataSourceFiltro(
          'uuid',
          this.searchData.uri,
          10,
        ),
        paginate: true,
        pageSize: 10,
        filter: this.searchData.filter || null,
      })
    } else if (this.objIsData == false) {
      this.crudService
        .getSingleData<any>(this.searchData.uri)
        .pipe()
        .subscribe(res => {
          this.dataSourceArray = res.dados
        })
    }
  }

  confirm() {
    if (!!this.searchData.isMultiple) {
      this.dialogRef.close(this.selectedRowKeys)
    } else {
      this.dialogRef.close(this.selectedRowKeys[0])
    }
  }

  cancel() {
    this.dialogRef.close(null)
  }

  onSelectionChanged(event: any) {
    this.selectedRowKeys = event.selectedRowsData
  }
}
