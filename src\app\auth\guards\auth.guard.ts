import { Injectable } from '@angular/core'
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router'
import { Observable } from 'rxjs'

import { LoginService } from './../services/login.service'

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(private router: Router, private loginService: LoginService) {}
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    const { currentUserValue, currentEntityValue } = this.loginService

    if (currentUserValue || location.href.includes('/resetar-senha')) {
      if (currentEntityValue) return true
      return true
    }

    this.router.navigate(['/login'], {
      queryParams: { returnUrl: state.url },
    })

    return false
  }
}
