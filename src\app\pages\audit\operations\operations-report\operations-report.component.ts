import { Component, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { CrudService } from '@common/services/crud.service'
import DataSource from 'devextreme/data/data_source'
import { finalize, take, takeUntil } from 'rxjs/operators'
import { OperationsService } from '../services/operations.service'

@Component({
  selector: 'eqp-operations-report',
  templateUrl: './operations-report.component.html',
  styleUrls: ['./operations-report.component.scss'],
})
export class OperationsReportComponent implements OnInit {
  public pageTitle = 'Relatório de Operações'
  public loading = false
  public model: FormGroup
  public baseUri: string = 'operacoes'
  public typeData: DataSource
  public gridData: DataSource

  constructor(
    private builder: FormBuilder,
    private operationsService: OperationsService,
  ) {}

  ngOnInit(): void {
    this.model = this.getNewModel()
  }

  private getNewModel() {
    return this.builder.group(
      {
        periodoInicial: [undefined, [Validators.required]],
        periodoFinal: [undefined, [Validators.required]],
        usuarioUuid: [],
      },
      {
        validator: (fg: FormGroup) => {
          const [d0, d1] = [
            fg.get('periodoInicial').value,
            fg.get('periodoFinal').value,
          ]
          if (new Date(d0).getTime() > new Date(d1).getTime()) {
            fg.get('periodoFinal').setErrors({
              customError:
                'O período inicial não deve ser maior que o período final.',
            })
          } else {
            fg.get('periodoFinal').setErrors(null)
          }
        },
      },
    )
  }

  eraseSearch() {
    this.model.reset()
    this.gridData = new DataSource([])
  }

  public getGridData() {
    const dto = this.model.getRawValue()
    dto.detalhar = 'S'

    this.loading = true
    this.operationsService
      .getDataByFilters(dto)
      .pipe(
        take(1),
        finalize(() => (this.loading = false)),
      )
      .subscribe(res => {
        this.gridData = new DataSource({
          store: {
            data: res.dados || [],
            key: 'uuid',
            type: 'array',
          },
          pageSize: 10,
          paginate: true,
        })
      })
  }
}
