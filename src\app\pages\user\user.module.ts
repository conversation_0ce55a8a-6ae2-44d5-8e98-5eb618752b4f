import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { CommonToolsModule } from '@common/common-tools.module'

import {
  DialogAlterPasswordComponent,
} from './profile/dialog-alter-password/dialog-alter-password.component'
import { ProfileComponent } from './profile/profile.component'
import { UserRoutingModule } from './user-routing.module'
import { UserComponent } from './user.component'

@NgModule({
  declarations: [UserComponent, ProfileComponent, DialogAlterPasswordComponent],
  imports: [CommonModule, UserRoutingModule, CommonToolsModule],
})
export class UserModule {}
