import { Component, Input, OnInit } from '@angular/core'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogRef } from '@nebular/theme'
import { first } from 'rxjs/operators'
import { LoginService } from '../services/login.service'

@Component({
  selector: 'eqp-senha',
  templateUrl: './senha.component.html',
  styleUrls: ['./senha.component.scss'],
})
export class SenhaComponent implements OnInit {
  @Input() public dados: any

  public model: FormGroup

  constructor(
    private builder: FormBuilder,
    protected ref: NbDialogRef<SenhaComponent>,
    private toastr: ToastrService,
    private service: LoginService,
  ) {}

  public ngOnInit(): void {
    this.model = this.getNewModel()
  }

  private getNewModel(): FormGroup {
    return this.builder.group({
      senha: ['', Validators.required],
      novaSenha: ['', Validators.required],
      novaSenhaConfirmacao: ['', Validators.required],
    })
  }

  public alterPassword(): void {
    if (this.validPassword() && this.mensagemErroSenha()) {
      this.dados.novaSenha = this.model.get('novaSenha').value
      this.dados.senha = this.model.get('senha').value
      this.dados.cpf = this.dados.payload.identificador
      console.log(this.dados)

      this.service
        .putSenha(this.dados)
        .pipe(first())
        .subscribe(() => {
          this.ref.close(true)
        })
    }
  }

  public closeAlterPassword(): void {
    this.ref.close(false)
  }

  private validPassword(): boolean {
    if (this.model.get('novaSenha').value) {
      if (
        this.model.get('novaSenha').value ===
        this.model.get('novaSenhaConfirmacao').value
      ) {
        if (
          this.model.get('senha').value !== this.model.get('novaSenha').value
        ) {
          return true
        } else {
          this.toastr.send({
            error: true,
            message: 'A nova senha tem que ser diferente da atual.',
          })
        }
      } else {
        this.toastr.send({
          error: true,
          message: 'A nova senha não confere com a confirmação',
        })
      }
    } else {
      this.toastr.send({
        error: true,
        message: 'É obrigatório preencher a nova senha',
      })
    }

    return false
  }

  public mensagemErroSenha(): boolean {
    let erro = true
    const novaSenha = this.model.get('novaSenha').value

    if (novaSenha && novaSenha.length < 8) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 8 (oito) caracteres',
      })
      erro = false
    }

    if (!RegExp(/\d/).test(novaSenha)) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 1 (um) número\n',
      })
      erro = false
    }

    if (!RegExp(/[A-Z]/).test(novaSenha)) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 1 (um) carácter maiúsculo',
      })
      erro = false
    }

    if (!RegExp(/[a-z]/).test(novaSenha)) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 1 (um) carácter minúsculo',
      })
      erro = false
    }

    if (!RegExp(/[!@#$%^&*(),.?":{}|<>\-_+]/).test(novaSenha)) {
      this.toastr.send({
        error: true,
        message: '• A senha deve ter no mínimo 1 (um) símbolo',
      })
      erro = false
    }

    return erro
  }
}
