import { Component, OnInit } from '@angular/core'
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms'
import { ActivatedRoute, Router } from '@angular/router'
import {
  ModalConfirmarComponent,
} from '@common/dialogs/modal-confirmar/modal-confirmar.component'
import { BaseTelasComponent } from '@common/misc/base-telas.component'
import { ToastrService } from '@common/services/toastr/toastr.service'
import { NbDialogService } from '@nebular/theme'
import { MenuService } from '@pages/menu.service'
import { finalize, first } from 'rxjs/operators'

import {
  NebularSelect,
} from './../../../../@design-tools/interfaces/nebular-select'
import { ClientsService } from './../clients.service'

@Component({
  selector: 'eqp-clients',
  templateUrl: './clients.component.html',
  styleUrls: ['./clients.component.scss'],
})
export class ClientsComponent extends BaseTelasComponent implements OnInit {
  public loading: boolean = false
  public pageTitle: string = 'Equiplano | Entidade'
  public formulario: FormGroup

  public statuses: NebularSelect[] = [
    {
      texto: 'Ativo',
      valor: 'ACTIVE',
    },
    {
      texto: 'Inativo',
      valor: 'INACTIVE',
    },
    {
      texto: 'Bloqueado',
      valor: 'BLOCKED',
    },
  ]
  public municipios: any

  constructor(
    private service: ClientsService,
    private toastr: ToastrService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private dialogService: NbDialogService,
    public menuService: MenuService,
  ) {
    super(menuService)
  }

  public ngOnInit(): void {
    this.formulario = this.getNovoFormulario()
    this.carregarTela()

    this.municipios = this.service.getDataSourceFiltro(
      'uuid',
      'cliente/municipios',
      10,
    )
  }

  private getNovoFormulario(): FormGroup {
    return this.formBuilder.group({
      cliente: this.formBuilder.group({
        uuid: [''],
        nome: ['', [Validators.required, Validators.maxLength(100)]],
        email: [
          '',
          [Validators.required, Validators.maxLength(100), Validators.email],
        ],
        telefone: ['', [Validators.required, Validators.maxLength(100)]],
        hostname: ['', [Validators.required, Validators.maxLength(100)]],
        logoEmailUrl: ['', [Validators.required, Validators.maxLength(100)]],
        municipioClienteUuid: ['', Validators.required],
        status: ['', Validators.required],
        corBase: ['', Validators.required],
        corTextoBase: ['', Validators.required],
        prefeitura: [false],
      }),
    })
  }

  private carregarTela(): void {
    this.route.params.pipe(first()).subscribe(params => {
      const uuid = params['uuid']
      if (uuid) this.buscar(uuid)
    })
  }

  private buscar(uuid: string): void {
    this.loading = true
    this.service
      .getIndividual(uuid)
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(data => {
        this.formulario.get('cliente').patchValue(data.dados)
      })
  }

  public cancelar(): void {
    this.gravarParametros()
    this.router.navigate([`equiplano/clientes`])
  }

  public remover(): void {
    const dialogRef = this.dialogService.open(ModalConfirmarComponent, {
      context: {},
      closeOnEsc: false,
      closeOnBackdropClick: false,
    })
    dialogRef.onClose.pipe(first()).subscribe(retorno => {
      if (retorno === 'S') {
        this.loading = true
        this.service
          .delete(this.formulario.get('cliente.uuid').value)
          .pipe(
            first(),
            finalize(() => {
              this.loading = false
            }),
          )
          .subscribe(
            () => {
              this.toastr.send({
                success: true,
                message: 'Cliente excluído com sucesso.',
              })
              this.cancelar()
            },
            (resp: any) => this.toastr.bulkSend(resp.mensagens),
          )
      }
    })
  }

  public gravar(): void {
    if (this.formulario.invalid) {
      this.toastr.send({
        error: true,
        message: 'Falta preencher os campos obrigatórios',
      })
      this.formulario.markAllAsTouched()
    } else {
      this.loading = true
      if (this.formulario.get('cliente.uuid').value) {
        this.atualizar()
      } else {
        this.novo()
      }
    }
  }

  private novo(): void {
    this.service
      .post(this.getClienteDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Cliente criado com sucesso.',
        })
        this.cancelar()
      })
  }

  private atualizar(): void {
    this.service
      .put(this.getClienteDto())
      .pipe(
        first(),
        finalize(() => {
          this.loading = false
        }),
      )
      .subscribe(() => {
        this.toastr.send({
          success: true,
          message: 'Cliente atualizado com sucesso.',
        })
        this.cancelar()
      })
  }

  private getClienteDto(): any {
    const dto = this.formulario.getRawValue()

    return dto.cliente
  }
}
