import { Component, OnInit } from '@angular/core'
import { SwUpdate } from '@angular/service-worker'
import { NbDialogService, NbMenuService } from '@nebular/theme'

import { ConfirmationComponent } from './../@common/dialogs/confirmation/confirmation.component'
import { ToastrService } from './../@common/services/toastr/toastr.service'
import { MenuService } from './menu.service'
import { first } from 'rxjs/operators'
import { MenuStorageService } from '@core/utils/menu-storage.service'

@Component({
  selector: 'eqp-pages',
  styleUrls: ['pages.component.scss'],
  template: `
    <eqp-one-column-layout>
      <nb-menu [items]="menu"></nb-menu>
      <router-outlet></router-outlet>
    </eqp-one-column-layout>
  `,
})
export class PagesComponent implements OnInit {
  menu = []

  constructor(
    private swUpdate: SwUpdate,
    private dialogService: NbDialogService,
    private toastr: ToastrService,
    private nbMenuService: NbMenuService,
    private menuService: MenuService,
    private menuStorageService: MenuStorageService,
  ) {
    this.updateClient()
  }

  public ngOnInit(): void {
    this.buildMenu()
  }

  private updateClient(): void {
    if (!this.swUpdate.isEnabled) {
      return
    }
    this.swUpdate.available.subscribe(event => {
      const content = {
        body: 'Deseja instalar a atualização?',
        confirmText: 'Instalar',
        confirmIcon: 'fas fa-sync-alt text-white',
        confirmType: 'success',
        cancelText: 'Adiar',
      }

      const dialogRef = this.dialogService.open(ConfirmationComponent, {
        closeOnEsc: false,
        closeOnBackdropClick: false,
      })

      dialogRef.componentRef.instance.dialogTitle = `Existe uma atualização para o sistema`
      dialogRef.componentRef.instance.confirmationContent = content

      dialogRef.onClose.subscribe((data: boolean) => {
        if (data) {
          this.toastr.send({
            success: true,
            message:
              'A atualização foi instalada com sucesso e será aplicada dentro dos próximos 3 segundos.',
          })
          setTimeout(() => {
            this.swUpdate.activateUpdate().then(() => location.reload())
          }, 3000)
        } else {
          this.toastr.send({
            warning: true,
            message: 'A atualização foi adiada.',
          })
          return
        }
      })
    })

    this.swUpdate.activated.subscribe(event => {})
  }

  private buildMenu(): void {
    if (this.menu.length === 0) {
      this.menuService
        .get()
        .pipe(first())
        .subscribe((data) => {
          const menuListWithUUID = this.menuStorageService.copyListWithUUID(data.dados);
          
          this.nbMenuService.addItems(menuListWithUUID, 'menu-sidebar');
          this.menu = menuListWithUUID;
          this.menuStorageService.setBreadcrumbData(menuListWithUUID)
        });
    }
  }
}
