{"name": "eqp-central", "short_name": "eqp-central", "theme_color": "#ffffff", "background_color": "#fafafa", "display": "minimal-ui", "scope": "./", "start_url": "./", "icons": [{"src": "assets/icons/equiplano-logo-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/icons/equiplano-logo-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/icons/equiplano-logo-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/icons/equiplano-logo-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/icons/equiplano-logo-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/icons/equiplano-logo-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/img/equiplano-logo-vertical.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "assets/img/equiplano-logo-vertical.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}]}