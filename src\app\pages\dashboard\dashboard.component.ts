import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { MenuService } from '@pages/menu.service'
import { first } from 'rxjs/operators'

@Component({
  selector: 'eqp-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  public loading: boolean = false
  public menus = []
  constructor(private menuService: MenuService, private router: Router) {}

  ngOnInit(): void {
    this.menuService
      .getTelaInicial()
      .pipe(first())
      .subscribe(data => {
        this.menus = data.dados
      })
  }

  public abrirRota(item): void {
    this.router.navigate([item.link])
  }
}
