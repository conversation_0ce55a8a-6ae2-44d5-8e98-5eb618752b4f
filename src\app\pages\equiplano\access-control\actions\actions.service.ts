import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import {
  ActionsInterface,
  ActionsReturnInterface,
} from '@pages/equiplano/access-control/actions/actions'
import { Observable } from 'rxjs'

import { ModulesReturnInterface } from '../modules/modules'

@Injectable({
  providedIn: 'root',
})
export class ActionsService extends CrudService {
  constructor(private http: HttpClient) {
    super(http)
  }

  public get(): Observable<ActionsReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ActionsReturnInterface>(`acao`, {
      headers,
    })
  }

  public getModulos(): Observable<ModulesReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ModulesReturnInterface>(`acao/modules`, {
      headers,
    })
  }

  public getPorModule(uuid): Observable<ActionsReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ActionsReturnInterface>(`acao/${uuid}/modulo`, {
      headers,
    })
  }

  public getPaiPorModule(uuid): Observable<ActionsReturnInterface> {
    const headers = new HttpHeaders()
    return this.http.get<ActionsReturnInterface>(`acao/dad/${uuid}/modulo`, {
      headers,
    })
  }

  public getIndividual(uuid: string): Observable<ActionsInterface[]> {
    const headers = new HttpHeaders()

    return this.http.get<ActionsInterface[]>(`acao/${uuid}`, {
      headers,
    })
  }

  public put(dto: ActionsInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<ActionsInterface>(`acao/${dto.uuid}`, dto, {
      headers,
      observe: 'response',
    })
  }

  public post(dto: ActionsInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<ActionsInterface>(`acao`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: ActionsInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<ActionsInterface[]>(`acao/lote`, batch, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: number): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`acao/${uuid}`, {
      headers,
    })
  }
}
