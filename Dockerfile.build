FROM node:10.16.0-slim
WORKDIR /tmp
RUN npm install --verbose @angular/cli && \
    npm install --verbose @angular-devkit/build-angular && \
    npm install --verbose @angular/compiler-cli && \
    npm install --verbose typescript && \
    npm install --verbose @angular/compiler && \
    npm install --verbose @angular/core
COPY package.json /tmp/
COPY .npmrc /tmp/
RUN npm install --verbose
COPY . .
RUN npm run build -- --configuration $environment --base-href /web --deploy-url /
WORKDIR /app
VOLUME /app
