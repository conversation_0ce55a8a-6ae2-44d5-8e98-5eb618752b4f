:host {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  --input-font-size: 11px;
}

nb-form-field {
  overflow: hidden;
}

::ng-deep .cdk-overlay-pane.input-search-tooltip-display-none {
  display: none !important;
}

.input-wrapper {
  position: relative;
  display: inline-flex;
  justify-content: flex-end;
  direction: rtl; 
}

nb-icon {
  font-size: 13px !important;
}

nb-form-field {
  direction: ltr; 
  overflow: visible;
}

input[nbInput] {
  direction: ltr;
  font-size: var(--input-font-size);
}

input[nbInput]::placeholder {
  font-size: var(--input-font-size);
}

.input-mirror {
  position: absolute;
  visibility: hidden;
  white-space: pre;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  letter-spacing: inherit;
  line-height: inherit;
  padding: 0 12px;
  border: none;
}
