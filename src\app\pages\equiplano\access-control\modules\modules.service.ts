import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { CrudService } from '@common/services/crud.service'
import {
  ModulesInterface,
} from '@pages/equiplano/access-control/modules/modules'
import { Observable } from 'rxjs'

@Injectable({
  providedIn: 'root',
})
export class ModulesService extends CrudService {
  constructor(private http: HttpClient) {
    super(http)
  }

  public get(): Observable<any> {
    const headers = new HttpHeaders()
    return this.http.get<any>(`auth/modulos?take=0`, {
      headers,
    })
  }

  public getIndividual(id: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.get<any>(`modulo/${id}`, {
      headers,
    })
  }

  public put(dto: ModulesInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.put<ModulesInterface>(`modulo/${dto.uuid}`, dto, {
      headers,
      observe: 'response',
    })
  }

  public post(dto: ModulesInterface): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<ModulesInterface>(`modulo`, dto, {
      headers,
      observe: 'response',
    })
  }

  public postBatch(batch: ModulesInterface[]): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.post<ModulesInterface[]>(`modulo/lote`, batch, {
      headers,
      observe: 'response',
    })
  }

  public delete(uuid: string): Observable<any> {
    const headers = new HttpHeaders()

    return this.http.delete<any>(`modulo/${uuid}`, {
      headers,
    })
  }
}
