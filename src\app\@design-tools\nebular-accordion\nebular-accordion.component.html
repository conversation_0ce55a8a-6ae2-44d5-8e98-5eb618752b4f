<ng-container>
  <nb-accordion
    [multi]="isMultiple"
    #accordion
  >
    <nb-accordion-item
      *ngFor="let item of items; index as i"
      [collapsed]="item?.collapsed"
      [expanded]="item?.expanded"
      [disabled]="item?.disabled"
      [hidden]="item?.hidden"
    >
      <nb-accordion-item-header [formGroup]="model">
        <div class="pdr-toggle"> {{ item?.title }} </div>
        <div *ngIf="showOpenedControl && i === 0">
          <eqp-nebular-toggle
            [value]="model.value.opened"
            [nebularLabel]="model.value.opened ? openedControlOnValue : openedControlOffValue"
            [useNebularLabel]="false"
            [nebularLabelPosition]="'end'"
            [status]="'basic'"
            [checked]="false"
            [disabled]="false"
            size="large"
            formControlName="opened"
            name="opened"
            class="float-right"
            [label]="openedControlLabel"
            (ngModelChange)="sectionController($event)"
          >
          </eqp-nebular-toggle>
        </div>
      </nb-accordion-item-header>
      <nb-accordion-item-body>
        <ng-container *ngTemplateOutlet="templates[item?.template]">
        </ng-container>
      </nb-accordion-item-body>
    </nb-accordion-item>
  </nb-accordion>
</ng-container>
