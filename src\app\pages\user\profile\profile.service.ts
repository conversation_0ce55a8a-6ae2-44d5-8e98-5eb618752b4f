import { HttpClient, HttpHeaders } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { UserDataService } from '@common/services/user-data.service'
import { UserInterface } from '@pages/administration/users/user'
import { Observable } from 'rxjs'
import { CriptService } from '../../../auth/login/cript.service'

@Injectable({
  providedIn: 'root',
})
export class ProfileService {
  constructor(
    private http: HttpClient,
    private userData: UserDataService,
    private service: CriptService,
  ) {}

  public get(): Observable<any> {
    const uuid = this.userData.getUserData()?.uuid
    const headers = new HttpHeaders()
    return this.http.get<any>(`usuario/${uuid}`, {
      headers,
    })
  }

  public put(dto: UserInterface): Observable<any> {
    const json = JSON.stringify(dto)

    const key = {
      chaveAleatoria: this.service.encrypt(json),
      chaveGerada: this.service.encrypt(json),
      chaveHash: this.service.encrypt(json),
      chaveAcesso: this.service.encrypt(json),
      chave: this.service.encrypt(json),
      keyRandom: this.service.encrypt(json),
      keyGenerate: this.service.encrypt(json),
      keyHash: this.service.encrypt(json),
      keyAcess: this.service.encrypt(json),
      key: this.service.encryptString(json),
    }

    const headers = new HttpHeaders()

    return this.http.put<UserInterface>(`auth/usuario/${dto.uuid}`, key, {
      headers,
      observe: 'response',
    })
  }
}
