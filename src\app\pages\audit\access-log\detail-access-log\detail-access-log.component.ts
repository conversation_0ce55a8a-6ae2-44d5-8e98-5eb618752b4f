import { Component, Input, OnInit } from '@angular/core';
import DataSource from 'devextreme/data/data_source';

@Component({
  selector: 'eqp-detail-access-log',
  templateUrl: './detail-access-log.component.html',
  styleUrls: ['./detail-access-log.component.scss']
})
export class DetailAccessLogComponent implements OnInit {
  @Input() logAccessData: any[] = []
  gridData: DataSource
  constructor() { }

  ngOnInit(): void {
    this.gridData = new DataSource({
      store: {
        data: this.logAccessData || [],
        key: 'usuarioUuid',
        type: 'array'
      },
      sort: [{selector: 'data', desc: true}],
      pageSize: 10,
      paginate: true
    })
  }

}
