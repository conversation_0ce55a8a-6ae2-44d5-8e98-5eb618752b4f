import { ConfigurationDto } from './configuration-dto'

export const EQP_APP_CONFIGURATION: ConfigurationDto = {
  app: {
    name: 'Central',
    title: 'Central',
    aboutTitle: 'Sobre o sistema central da Equiplano',
    idSistema: '45f7bc3d-3aaa-2082-0abd-dd6ff6b063c1',
    sidebar: {
      left: true,
      right: false,
    },
    layout: {
      windowMode: false,
    },
  },
  header: {
    title: 'Central',
    showNotifications: false,
    showFavorites: true,
    showNewWindow: true,
    showThemeSelector: true,
    showSystemSelector: true,
    showNewsletter: true,
    themes: [
      {
        value: 'default',
        name: 'Pa<PERSON><PERSON>',
      },
      {
        value: 'dark',
        name: 'Escuro',
      },
      {
        value: 'cosmic',
        name: 'Cósmico',
      },
      {
        value: 'corporate',
        name: 'Corporativo',
      },
      {
        value: 'material-light',
        name: 'Material',
      },
      {
        value: 'material-dark',
        name: 'Material escuro',
      },
    ],
    userMenu: [
      {
        title: 'Perfil',
        icon: 'person-outline',
        // options: { animation: { type: 'pulse' } },
        link: '/usuario/perfil',
      },
      {
        title: 'Nova janela',
        icon: 'browser-outline',
      },
      {
        title: 'Tema',
        icon: 'color-palette-outline',
        children: [
          {
            data: 'default',
            title: 'Clássico',
          },
          {
            data: 'dark',
            title: 'Escuro',
          },
          {
            data: 'cosmic',
            title: 'Cósmico',
          },
          {
            data: 'corporate',
            title: 'Corporativo',
          },
          {
            data: 'material-light',
            title: 'Material',
          },
          {
            data: 'material-dark',
            title: 'Material escuro',
          },
        ],
      },
      {
        title: 'Sobre',
        icon: 'question-mark-circle-outline',
        link: '/outros/sobre',
      },
      { title: 'Sair', icon: 'log-out-outline' },
    ],
    themeMenu: [
      {
        data: 'default',
        title: 'Clássico',
        icon: 'sun',
      },
      {
        data: 'dark',
        title: 'Escuro',
        icon: 'moon',
      },
      {
        data: 'cosmic',
        title: 'Cósmico',
        icon: { icon: 'meteor', pack: 'fas' },
      },
      {
        data: 'corporate',
        title: 'Corporativo',
        icon: { icon: 'landmark', pack: 'fas' },
      },
      {
        data: 'material-light',
        title: 'Material',
        icon: 'sun-outline',
      },
      {
        data: 'material-dark',
        title: 'Material escuro',
        icon: 'moon-outline',
      },
    ],
  },
  footer: {
    title: 'Equiplano sistemas',
    showLinkedin: true,
    linkedinLink: 'https://www.linkedin.com/company/equiplano',
    showInstagram: true,
    instagramLink: 'https://www.instagram.com/equiplano.sistemas/',
    showFacebook: true,
    facebookLink: 'https://www.facebook.com/equiplano',
    showTwitter: false,
    twitterLink: '#',
  },
  enviroment: {
    dev: 'https://api-dev.ops.equiplano.com.br',
    homolog: 'https://api-homolog.ops.equiplano.com.br',
    apresentacao: 'https://api-apresentacao.ops.equiplano.com.br',
    treinamento: 'https://api-treinamento.ops.equiplano.com.br',
    prod: 'https://api.equiplano.cloud',
  },
  system: {
    id: '',
  },
  backend: {
    mainUrl: '',
  },
}
