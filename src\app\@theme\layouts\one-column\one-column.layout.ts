import { Component } from '@angular/core'

import { EQP_APP_CONFIGURATION } from './../../../../eqp-configuration'

@Component({
  selector: 'eqp-one-column-layout',
  styleUrls: ['./one-column.layout.scss'],
  //ADD THIS windowMode IF YOU WANNA WINDOWMODE AFTER CERTAIN RESOLUTION
  template: `
    <nb-layout
      [windowMode]="config?.app?.layout?.windowMode"
      [withScroll]="true"
    >
      <nb-layout-header fixed>
        <eqp-header></eqp-header>
      </nb-layout-header>

      <nb-sidebar class="menu-sidebar" tag="menu-sidebar" responsive>
        <ng-content select="nb-menu"></ng-content>
      </nb-sidebar>

      <nb-layout-column>
        <ng-content select="router-outlet"></ng-content>
      </nb-layout-column>

      <nb-layout-footer fixed>
        <eqp-footer></eqp-footer>
      </nb-layout-footer>
    </nb-layout>
  `,
})
export class OneColumnLayoutComponent {
  public config: typeof EQP_APP_CONFIGURATION = EQP_APP_CONFIGURATION
}
