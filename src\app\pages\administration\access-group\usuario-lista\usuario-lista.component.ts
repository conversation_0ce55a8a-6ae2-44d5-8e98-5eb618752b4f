import { Component, Input, OnInit } from '@angular/core'
import { NbDialogRef } from '@nebular/theme'
import DataSource from 'devextreme/data/data_source'
import { AccessGroupService } from '../access-group.service'

@Component({
  selector: 'eqp-usuario-lista',
  templateUrl: './usuario-lista.component.html',
  styleUrls: ['./usuario-lista.component.scss'],
})
export class UsuarioListaComponent implements OnInit {
  @Input()
  public grupoAcesso: any
  public gridData: any
  selectedItemKeys: string[] = []

  public constructor(
    protected ref: NbDialogRef<UsuarioListaComponent>,
    private service: AccessGroupService,
  ) {}

  ngOnInit(): void {
    this.fetchGrid()
  }

  private fetchGrid(): void {
    this.gridData = new DataSource({
      store: this.service.getDataSourceFiltroCondicao(
        'uuid',
        `grupo_acesso/cmb-usuarios`,
        10,
      ),
      paginate: true,
      pageSize: 10,
    })
  }

  public dismiss(): void {
    this.ref.close([])
  }

  onSelectionChanged({ selectedRowKeys }: any): void {
    this.selectedItemKeys = selectedRowKeys
  }

  public selecionar(): void {
    this.ref.close(this.selectedItemKeys)
  }
}
