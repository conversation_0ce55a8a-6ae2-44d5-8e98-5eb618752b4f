import { Component, OnInit } from '@angular/core';
import { normalizarString } from '@common/helpers/string-normalizer';
import { NewsletterInterface } from '@pages/newsletter/newsletter';
import { NewsletterService } from '@pages/newsletter/newsletter.service';

@Component({
  selector: 'eqp-newsletter-info',
  templateUrl: './newsletter-info.component.html',
  styleUrls: ['./newsletter-info.component.scss']
})
export class NewsletterInfoComponent implements OnInit {
  public boletins: NewsletterInterface[] = []
  public modulos = []
  public modulosFiltrados = []

  public termoPesquisaModulo = ''

  constructor(
    private newsletterService: NewsletterService,
  ) { }

  ngOnInit(): void {
    this.newsletterService.loadModules().subscribe(res => {
      this.modulos = res.data.sort((a, b) => a.descricao.localeCompare(b.descricao))
      this.modulosFiltrados = [...this.modulos] 
    })
  }

  onPesquisaModuloChange(termo: string): void {
    this.termoPesquisaModulo = termo
    this.filtrarModulos()
  }

  limparPesquisaModulo(): void {
    this.termoPesquisaModulo = ''
    this.filtrarModulos()
  }

  private filtrarModulos(): void {
    if (!this.termoPesquisaModulo.trim()) {
      this.modulosFiltrados = [...this.modulos]
    } else {
      const termoLower = normalizarString(this.termoPesquisaModulo.toLowerCase().trim())
      this.modulosFiltrados = this.modulos.filter(modulo => {
        const descricaoModulo = normalizarString(modulo.descricao)
        return descricaoModulo.includes(termoLower)
      }
      )
    }
  }
}
