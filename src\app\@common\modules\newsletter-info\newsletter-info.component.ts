import { Component, OnInit } from '@angular/core';
import { NewsletterInterface } from '@pages/newsletter/newsletter';
import { NewsletterService } from '@pages/newsletter/newsletter.service';

@Component({
  selector: 'eqp-newsletter-info',
  templateUrl: './newsletter-info.component.html',
  styleUrls: ['./newsletter-info.component.scss']
})
export class NewsletterInfoComponent implements OnInit {
  public boletins: NewsletterInterface[] = []
  public modulos = []
  public modulosFiltrados = []

  // Controle de pesquisa de módulos
  public termoPesquisaModulo = ''

  constructor(
    private newsletterService: NewsletterService,
  ) { }

  ngOnInit(): void {
    this.newsletterService.loadModules().subscribe(res => {
      this.modulos = res.data.sort((a, b) => a.descricao.localeCompare(b.descricao))
      this.modulosFiltrados = [...this.modulos] // Inicializar com todos os módulos
    })
  }

  onPesquisaModuloChange(termo: string): void {
    this.termoPesquisaModulo = termo
    this.filtrarModulos()
  }

  limparPesquisaModulo(): void {
    this.termoPesquisaModulo = ''
    this.filtrarModulos()
  }

  private filtrarModulos(): void {
    if (!this.termoPesquisaModulo.trim()) {
      this.modulosFiltrados = [...this.modulos]
    } else {
      const termoLower = this.termoPesquisaModulo.toLowerCase().trim()
      this.modulosFiltrados = this.modulos.filter(modulo =>
        modulo.descricao?.toLowerCase().includes(termoLower)
      )
    }
  }
}
