import { Component, OnInit } from '@angular/core';
import { NewsletterInterface } from '@pages/newsletter/newsletter';
import { NewsletterService } from '@pages/newsletter/newsletter.service';
import { BehaviorSubject } from 'rxjs';

@Component({
  selector: 'eqp-newsletter-info',
  templateUrl: './newsletter-info.component.html',
  styleUrls: ['./newsletter-info.component.scss']
})
export class NewsletterInfoComponent implements OnInit {
  public boletins: NewsletterInterface[] = []
  public modulos = []

  // Observable para o termo de pesquisa
  public termoPesquisa$ = new BehaviorSubject<string>('')
  public termoPesquisa = ''

  constructor(
    private newsletterService: NewsletterService,
  ) { }

  ngOnInit(): void {
    this.newsletterService.loadModules().subscribe(res => {
      this.modulos = res.data.sort((a, b) => a.descricao.localeCompare(b.descricao))
    })
  }

  onPesquisaChange(termo: string): void {
    this.termoPesquisa = termo
    this.termoPesquisa$.next(termo)
  }

  limparPesquisa(): void {
    this.termoPesquisa = ''
    this.termoPesquisa$.next('')
  }
}
