import { Component, OnInit } from '@angular/core';
import { NewsletterInterface } from '@pages/newsletter/newsletter';
import { NewsletterService } from '@pages/newsletter/newsletter.service';

@Component({
  selector: 'eqp-newsletter-info',
  templateUrl: './newsletter-info.component.html',
  styleUrls: ['./newsletter-info.component.scss']
})
export class NewsletterInfoComponent implements OnInit {
  public boletins: NewsletterInterface[] = []
  public modulos = []

  constructor(
    private newsletterService: NewsletterService,
  ) { }

  ngOnInit(): void {
    this.newsletterService.loadModules().subscribe(res => {
      this.modulos = res.data.sort((a, b) => a.descricao.localeCompare(b.descricao))
    })
    
  }
}
