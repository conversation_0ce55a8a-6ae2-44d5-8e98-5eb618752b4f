<nb-card class="teste">
  <nb-card-header>Selecione os usuários </nb-card-header>
  <nb-card-body>
    <dx-data-grid
      id="usuarioGrid"
      [dataSource]="gridData"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [showColumnLines]="false"
      [showRowLines]="false"
      [showBorders]="false"
      [rowAlternationEnabled]="true"
      [wordWrapEnabled]="true"
      [loadPanel]="false"
      [columnHidingEnabled]="true"
      [remoteOperations]="true"
      keyExpr="uuid"
      (onSelectionChanged)="onSelectionChanged($event)"
    >
      <dxo-paging [pageSize]="10"></dxo-paging>
      <dxo-pager
        [showInfo]="true"
        [showNavigationButtons]="true"
        [showPageSizeSelector]="false"
      >
      </dxo-pager>

      <dxo-header-filter [visible]="false"> </dxo-header-filter>
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <dxo-sorting mode="multiple"></dxo-sorting>

      <dxo-selection mode="multiple" [allowSelectAll]="false"></dxo-selection>

      <dxo-column-chooser [enabled]="false"></dxo-column-chooser>

      <dxo-group-panel
        [visible]="false"
        [emptyPanelText]="''"
      ></dxo-group-panel>

      <dxo-search-panel
        [visible]="true"
        placeholder="Buscar usu[ario]"
      ></dxo-search-panel>

      <dxo-editing
        mode="form"
        [allowUpdating]="false"
        [allowDeleting]="false"
        [allowAdding]="false"
        [useIcons]="false"
      >
      </dxo-editing>

      <dxi-column dataField="nome" caption="Nome"></dxi-column>

      <dxi-column
        dataField="uuid"
        caption=""
        [width]="80"
        [allowFiltering]="false"
        [allowSorting]="false"
        [visible]="false"
      ></dxi-column>
    </dx-data-grid>
  </nb-card-body>
  <nb-card-footer>
    <button type="button" class="btn btn-dark" (click)="dismiss()">
      Cancelar
    </button>
    <button
      type="button"
      class="btn btn-success float-md-right"
      (click)="selecionar()"
    >
      Gravar
    </button>
  </nb-card-footer>
</nb-card>
