<!--
ATTENTION! WE WILL HAVE TO CLOSE THIS ISSUE if you don't provide the needed information.
Please read https://github.com/akveo/ngx-admin/blob/master/CONTRIBUTING.md before opening an issue.
-->

### Issue type

**I'm submitting a ...**  (check one with "x")

* [ ] bug report
* [ ] feature request
* [ ] question about the decisions made in the repository

### Issue description

**Current behavior:**
<!-- Describe how the bug manifests. -->

**Expected behavior:**
<!-- Describe what the behavior would be without the bug. -->

**Steps to reproduce:**
<!--  Please explain the steps required to duplicate the issue, especially if you are able to provide a sample application. -->

**Related code:**
<!-- 
If you are able to illustrate the bug or feature request with an example, please provide a sample application via one of the following means:

A sample application via GitHub

StackBlitz (https://stackblitz.com)

Plunker (http://plnkr.co/edit/cpeRJs?p=preview)

-->

```
insert short code snippets here
```

### Other information:

**npm, node, OS, Browser**
```
<!--
Node, npm: `node --version` and `npm --version`
OS: Windows (7/8/10). Linux (incl. distribution). macOS (El Capitan? Sierra?)
Browser: Chrome/Safari/Firefox/etc?
-->
```

**Angular, Nebular**
```
<!--
Check your `package-lock.json` or locate a `package.json` in the `node_modules` folder.
-->
```
