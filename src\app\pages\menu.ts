import { NbMenuItem } from '@nebular/theme'

export const MENU_ITEMS: NbMenuItem[] = [
  {
    title: 'Central',
    icon: { icon: 'home', pack: 'fas' },
    link: '/',
  },
  {
    title: 'Administração',
    icon: { icon: 'user-cog', pack: 'fas' },
    children: [
      {
        title: 'Usuários',
        icon: { icon: 'users', pack: 'fas' },
        link: '/administracao/usuarios',
      },
      {
        title: 'Configuração de e-mail',
        icon: { icon: 'mail-bulk', pack: 'fas' },
        link: '/administracao/configuracao-email',
      },
    ],
  },
  {
    title: 'Equiplano',
    icon: { icon: 'cloud', pack: 'fas' },
    children: [
      {
        title: 'Clientes',
        icon: { icon: 'user-tie', pack: 'fas' },
        link: '/equiplano/clientes',
      },
      {
        title: 'Controle de acesso',
        icon: { icon: 'user-shield', pack: 'fas' },
        children: [
          {
            title: 'Sub módulos',
            icon: { icon: 'cubes', pack: 'fas' },
            link: '/equiplano/controle-acesso/sub-modulos',
          },
          {
            title: 'Módulos',
            icon: { icon: 'cube', pack: 'fas' },
            link: '/equiplano/controle-acesso/acoes',
          },
          {
            title: 'Grupo de acesso',
            icon: { icon: 'object-group', pack: 'fas' },
            link: '/equiplano/controle-acesso/grupo-acesso',
          },
          {
            title: 'Licenças',
            icon: { icon: 'id-badge', pack: 'fas' },
            link: '/equiplano/controle-acesso/licencas',
          },
          {
            title: 'Micro serviços',
            icon: { icon: 'project-diagram', pack: 'fas' },
            link: '/equiplano/controle-acesso/micro-servicos',
          },
          {
            title: 'Parâmetros',
            icon: { icon: 'sliders-h', pack: 'fas' },
            link: '/equiplano/controle-acesso/parametros',
          },
        ],
      },
    ],
  },
]
