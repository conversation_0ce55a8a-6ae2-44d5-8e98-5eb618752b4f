import { Component, OnInit } from '@angular/core'
import { Title } from '@angular/platform-browser'
import { AnalyticsService } from '@core/utils/analytics.service'
import { SeoService } from '@core/utils/seo.service'

import { EQP_APP_CONFIGURATION as eqpConfig } from '../eqp-configuration'
import { CrossStorageService } from './auth/services/cross-storage.service'

/**
 * @license
 * Copyright Akveo. All Rights Reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 */
@Component({
  selector: 'eqp-app',
  template: '<router-outlet></router-outlet>',
})
export class AppComponent implements OnInit {
  constructor(
    private analytics: AnalyticsService,
    private seoService: SeoService,
    private titleService: Title,
    private crossStorageService: CrossStorageService,
  ) {}

  ngOnInit(): void {
    this.analytics.trackPageViews()
    this.seoService.trackCanonicalChanges()
    this.titleService.setTitle(eqpConfig.app.title)
    this.crossStorageService.registerHub()
  }
}
